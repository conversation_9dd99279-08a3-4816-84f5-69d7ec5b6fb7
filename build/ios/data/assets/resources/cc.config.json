{"importBase": "import", "nativeBase": "native", "name": "resources", "deps": [], "uuids": ["01YiY+IbtIxbbAbZevIyjb", "35QYurHgpBCKJnzoyMJCLD", "37/vjZWRdHs6QPTirOa/Mo", "01YiY+IbtIxbbAbZevIyjb@6c48a", "01YiY+IbtIxbbAbZevIyjb@f9941", "01959b579", "0228435c6", "02mF77AhdN+LUmE3XYF0kl", "02mF77AhdN+LUmE3XYF0kl@6c48a", "02mF77AhdN+LUmE3XYF0kl@f9941", "05b737039", "050J0q0eZNtLfxRsgoVh4c", "08B4lpzuxG1rZggt15ITjW", "08B4lpzuxG1rZggt15ITjW@6c48a", "08B4lpzuxG1rZggt15ITjW@f9941", "08WEhbSwlLXp/E6vy85tyV", "08WEhbSwlLXp/E6vy85tyV@6c48a", "08WEhbSwlLXp/E6vy85tyV@f9941", "09b90c6a5", "09bd04adc", "0awjJEm0lOZrgmq6QRyaFo", "0awjJEm0lOZrgmq6QRyaFo@6c48a", "0awjJEm0lOZrgmq6QRyaFo@f9941", "0a4no7LTdJeJbqYmu+zkD1", "0bc3ecf13", "0c2a51634", "0cL/pExSVCJJTyiVUFvr4v", "0cL/pExSVCJJTyiVUFvr4v@6c48a", "0cL/pExSVCJJTyiVUFvr4v@f9941", "0d882e0be", "0eCSxFxllMcKJHn+Zt3WsU", "0e09c4e9e", "0egy8lrORML7so3k+5KNar", "0ea25dec6", "12RRXWwB1LJKl47vxH4wxN", "19NQYs6hxDo54hyWTAcezs", "19NQYs6hxDo54hyWTAcezs@6c48a", "19NQYs6hxDo54hyWTAcezs@f9941", "24pwTaKGdEbY0aXpIMdeCd", "24pwTaKGdEbY0aXpIMdeCd@6c48a", "24pwTaKGdEbY0aXpIMdeCd@f9941", "2eY9bbyBRJlJmqIrqHyQe+", "2eY9bbyBRJlJmqIrqHyQe+@6c48a", "2eY9bbyBRJlJmqIrqHyQe+@f9941", "38QUxvzghAw7CgTyTOEDHe", "46ZtbII3ZN9L4o1m44O6rO", "4dUynpj2dD46OZ4PqaWtX0", "38QUxvzghAw7CgTyTOEDHe@6c48a", "38QUxvzghAw7CgTyTOEDHe@f9941", "39eg7OsTdHT4nt3iOt2s82", "39eg7OsTdHT4nt3iOt2s82@6c48a", "39eg7OsTdHT4nt3iOt2s82@f9941", "46sRigKlhMW5IX+i4qIOyV", "525Wn55e9KfJgHyymrwV2G", "5eEihD7PJC56t07Bn3AkCj", "46sRigKlhMW5IX+i4qIOyV@6c48a", "46sRigKlhMW5IX+i4qIOyV@f9941", "47GlVnlw5PwJBWYUoInIUN", "48+ka/4zRPd7jluBsyzDhG", "4942ZUssNH2a/rXbKChqGw", "4942ZUssNH2a/rXbKChqGw@6c48a", "4942ZUssNH2a/rXbKChqGw@f9941", "4dTnGVbIRGIY08N/tncyFi", "5fpKAgrRJNUZ59V87NwFbK", "4dTnGVbIRGIY08N/tncyFi@6c48a", "4dTnGVbIRGIY08N/tncyFi@f9941", "64L0xOOXlCdJWUPIKGpX+y", "6bdWlc3lhLVIbYUgvhZgFc", "74VPnxR5ZAWr1nzOODXOSh", "74VPnxR5ZAWr1nzOODXOSh@6c48a", "74VPnxR5ZAWr1nzOODXOSh@f9941", "76Xaao4HRKO4G3ypcQ+cLT", "76Xaao4HRKO4G3ypcQ+cLT@6c48a", "76Xaao4HRKO4G3ypcQ+cLT@f9941", "7bOlT0MSVEG6duaggQ3bCY", "7bOlT0MSVEG6duaggQ3bCY@6c48a", "7bOlT0MSVEG6duaggQ3bCY@f9941", "7bzlqIK8NFS5ZUlrYuHKKf", "7bzlqIK8NFS5ZUlrYuHKKf@6c48a", "7bzlqIK8NFS5ZUlrYuHKKf@f9941", "7cdSnkVxBOlK10Rczz+Eu8", "7f46LBRONF7aOkgekKaZTH", "7cdSnkVxBOlK10Rczz+Eu8@6c48a", "7cdSnkVxBOlK10Rczz+Eu8@f9941", "801lteK0lAEJnaaSVvh2rV", "81Ugz1OgJGO7PsyIcKP0lA", "8bta0l+0VB6ps5YQ1tWRV8", "8diGfN9ZFF+oVk5DX82xhH", "b7HBnQSslNy6oytl/eLEBq", "801lteK0lAEJnaaSVvh2rV@6c48a", "801lteK0lAEJnaaSVvh2rV@f9941", "81lKEvJyRNgqSD3Fi+L3ZL", "86CHtLQoRLkYcH+h9xQG6h", "86CHtLQoRLkYcH+h9xQG6h@6c48a", "86CHtLQoRLkYcH+h9xQG6h@f9941", "87Z2aRCYhKT4I8nt4BpA4c", "8cw9emvcZIcq+Ie5l5XgnD", "a4Yof/90tAdaSDCLECf+ct", "8cw9emvcZIcq+Ie5l5XgnD@6c48a", "8cw9emvcZIcq+Ie5l5XgnD@f9941", "91zBdqf65O+ql7U8yxjWP5", "91zBdqf65O+ql7U8yxjWP5@6c48a", "91zBdqf65O+ql7U8yxjWP5@f9941", "96Ko+uxlZOSrwzArDb55P1", "9dcxrjryVAf7WISnsGLDdt", "9fho2hieRMpqThK1kpgdsh", "9f2QDdIhtPiY8s+6NCQ8g1", "9f2QDdIhtPiY8s+6NCQ8g1@6c48a", "9f2QDdIhtPiY8s+6NCQ8g1@f9941", "a2o+kMk39O+aLa8g1EXSFK", "a2o+kMk39O+aLa8g1EXSFK@6c48a", "a2o+kMk39O+aLa8g1EXSFK@f9941", "b1NYQM9CdNML4fiV8q58Y8", "b1NYQM9CdNML4fiV8q58Y8@6c48a", "b1NYQM9CdNML4fiV8q58Y8@f9941", "b96V8CSuxMAYNDHGzgNrDG", "b96V8CSuxMAYNDHGzgNrDG@6c48a", "b96V8CSuxMAYNDHGzgNrDG@f9941", "bdG8q6vX1KcbFDmXyII4Pk", "cfgEVVfXVHIIpUT4oTSCdf", "d1MS8CTn9Pn67X8lWrtuf4", "e1B5EMDvhKa461RtKwll8j", "e72a9NVA1O+4q3MK0l4xIh", "bdG8q6vX1KcbFDmXyII4Pk@f9941", "c18s46WqhFuqs+X/+LuHr5", "c4kqHHgpBNcZj/pLuCLLBn", "cbW/VfeH1EGKGw8h1EA4TO", "d59Z8QQ85C5Z5W9SGPQpyp", "d59Z8QQ85C5Z5W9SGPQpyp@6c48a", "d59Z8QQ85C5Z5W9SGPQpyp@f9941", "d6xdsFp7VMObKfichsMV3P", "d6xdsFp7VMObKfichsMV3P@6c48a", "d6xdsFp7VMObKfichsMV3P@f9941", "dec5GXjXRCFb/ngYogPNxm", "dfFrs6vHdHZrrnJ2kxbtbl", "e2xXiqoMhGvrzW8c24GPG6", "ff3ACRArVH6Zy6dSrQ9/Jg", "bdG8q6vX1KcbFDmXyII4Pk@6c48a", "e2xXiqoMhGvrzW8c24GPG6@6c48a", "e2xXiqoMhGvrzW8c24GPG6@f9941", "e8+xUTHKVPGYsep4lB9GUs", "e8+xUTHKVPGYsep4lB9GUs@6c48a", "e8+xUTHKVPGYsep4lB9GUs@f9941", "edYR225R5KU7YkhI3UWs4h", "edYR225R5KU7YkhI3UWs4h@6c48a", "edYR225R5KU7YkhI3UWs4h@f9941", "eeqXyZcRBCOaHN6Xs7VEHD", "eeqXyZcRBCOaHN6Xs7VEHD@6c48a", "eeqXyZcRBCOaHN6Xs7VEHD@f9941", "f8kRbVjX5AL5lVMoUYjXje", "fckx7P6YFIzJmfEy7cpy+m", "fckx7P6YFIzJmfEy7cpy+m@6c48a", "fckx7P6YFIzJmfEy7cpy+m@f9941"], "paths": {"1": ["prefab/paints/paint_pink", 0, 1], "2": ["prefab/cars/car-1", 0, 1], "11": ["sound/Plop", 1, 1], "30": ["prefab/levels/level-4", 0, 1], "32": ["prefab/weapons/explosion/dartexplosion", 0, 1], "34": ["prefab/weapons/explosion/bulletexplosion", 0, 1], "45": ["prefab/paints/paint_yellow", 0, 1], "46": ["prefab/paints/PaintRoot", 0, 1], "53": ["prefab/weapons/dart", 0, 1], "54": ["prefab/levels/level-1", 0, 1], "57": ["prefab/cars/car-4", 0, 1], "58": ["prefab/cars/car-3", 0, 1], "63": ["prefab/cars/healthBar", 0, 1], "66": ["prefab/cars/car-5", 0, 1], "67": ["sound/crash", 1, 1], "81": ["prefab/weapons/rocket", 0, 1], "85": ["prefab/loading", 0, 1], "86": ["prefab/levels/level-5", 0, 1], "87": ["prefab/levels/level-3", 0, 1], "88": ["prefab/paints/paint_orange", 0, 1], "95": ["prefab/cars/car-2", 0, 1], "97": ["prefab/paints/paint_purple", 0, 1], "103": ["prefab/paints/README", 2, 1], "105": ["sound/engine_on", 1, 1], "119": ["prefab/levels/level-2", 0, 1], "120": ["prefab/paints/paint_blue", 0, 1], "121": ["prefab/weapons/explosion/explosion", 0, 1], "122": ["prefab/paints/paint_red", 0, 1], "124": ["sound/drift", 1, 1], "125": ["sound/bgm", 1, 1], "126": ["sound/running", 1, 1], "134": ["sound/explode", 1, 1], "136": ["prefab/weapons/bullet", 0, 1], "149": ["sound/tires_squal_loop", 1, 1]}, "scenes": {}, "packs": {"01959b579": [4, 14, 17, 22, 28, 30, 32, 34, 37, 40, 43, 1, 48, 45, 56, 65, 46, 53, 63, 73, 76, 79, 83, 81, 90, 94, 99, 102, 108, 111, 97, 88, 117, 132, 121, 145, 152, 136], "0228435c6": [21, 27, 42, 64, 75, 78, 82, 98, 107, 39, 55, 60, 89, 101, 50, 72, 8, 69, 47, 93, 3, 16, 36, 13, 110, 116, 113, 137, 128, 131, 138, 141, 144, 147, 151], "05b737039": [51, 129], "09b90c6a5": [54, 70], "09bd04adc": [2, 61, 139, 122], "0bc3ecf13": [85, 123], "0c2a51634": [87, 142], "0d882e0be": [86, 114], "0e09c4e9e": [120, 148], "0ea25dec6": [9, 119]}, "versions": {"import": [], "native": []}, "redirect": [], "debug": false, "extensionMap": {".ccon": [104, 133, 23, 91]}, "hasPreloadScript": true, "dependencyRelationships": {}, "types": ["cc.Prefab", "cc.AudioClip", "cc.TextAsset"]}