[1, ["5fpKAgrRJNUZ59V87NwFbK", "86CHtLQoRLkYcH+h9xQG6h@f9941", "ff3ACRArVH6Zy6dSrQ9/Jg", "525Wn55e9KfJgHyymrwV2G", "7f46LBRONF7aOkgekKaZTH", "4dUynpj2dD46OZ4PqaWtX0", "4942ZUssNH2a/rXbKChqGw@f9941", "38QUxvzghAw7CgTyTOEDHe@f9941", "d6xdsFp7VMObKfichsMV3P@f9941", "edYR225R5KU7YkhI3UWs4h@f9941", "b1NYQM9CdNML4fiV8q58Y8@f9941", "b7HBnQSslNy6oytl/eLEBq", "a4Yof/90tAdaSDCLECf+ct", "35QYurHgpBCKJnzoyMJCLD", "46ZtbII3ZN9L4o1m44O6rO", "b1NYQM9CdNML4fiV8q58Y8@6c48a"], ["node", "targetInfo", "target", "root", "asset", "_spriteFrame", "source", "_parent", "destroyedSprite", "paintPrefab", "normalBulletPrefab", "dartPrefab", "rocketPrefab", "data", "_textureSource"], [["cc.Node", ["_name", "_layer", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_components", "_parent", "_children", "_lpos"], -1, 4, 9, 1, 2, 5], ["91c082Rgh1HBbV2vKQl2J1W", ["maxSpeed", "acceleration", "turnSpeed", "maxHealth", "paintSprayInterval", "weaponType", "fireRate", "node", "__prefab"], -4, 1, 4], ["cc.Node", ["_name", "_mobility", "_layer", "_children", "_components", "_prefab", "_lpos", "_lrot", "_euler", "_lscale"], 0, 2, 12, 4, 5, 5, 5, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.TargetOverrideInfo", ["propertyPath", "target", "targetInfo", "source"], 2, 1, 4, 1], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.Sprite", ["_sizeMode", "node", "__prefab", "_spriteFrame"], 2, 1, 4, 6], ["cc.PolygonCollider2D", ["node", "__prefab", "_offset", "_points"], 3, 1, 4, 5, 12], ["cc.PrefabInfo", ["fileId", "instance", "root", "asset", "targetOverrides", "nestedPrefabInstanceRoots"], 1, 1, 1, 9, 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.TargetInfo", ["localID"], 2], ["cc.RigidBody2D", ["enabledContactListener", "_type", "_gravityScale", "node", "__prefab"], 0, 1, 4], ["cc.BoxCollider2D", ["_friction", "node", "__prefab", "_size"], 2, 1, 4, 5], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides"], 2, 1, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8]], [[7, 0, 2], [13, 0, 2], [18, 0, 1, 2, 2], [11, 0, 1, 2, 3, 4, 5, 5], [0, 2, 3, 6, 4, 3], [3, 0, 1, 2, 1], [8, 0, 1, 2, 3, 2], [12, 0, 1, 2, 3, 4, 5, 4], [16, 0, 1, 2, 2], [17, 0, 1, 2, 3], [19, 0, 1, 2, 3], [4, 0, 1, 2, 2], [4, 0, 3, 1, 2, 2], [14, 0, 1, 2, 3, 4, 4], [15, 0, 1, 2, 3, 2], [20, 0, 1, 2, 2], [2, 0, 1, 2, 3, 4, 5, 6, 7, 9, 8, 4], [3, 0, 1, 1], [6, 0, 2], [0, 0, 1, 7, 5, 4, 3], [0, 0, 1, 6, 7, 5, 4, 8, 3], [0, 0, 1, 6, 5, 4, 3], [2, 0, 1, 2, 3, 4, 5, 6, 7, 8, 4], [9, 0, 1, 2, 3, 1], [10, 0, 1, 2, 3, 4, 5, 3], [1, 0, 1, 2, 3, 4, 6, 7, 8, 7], [1, 0, 1, 2, 3, 4, 5, 7, 8, 7], [1, 0, 1, 2, 3, 4, 6, 5, 7, 8, 8], [1, 0, 1, 2, 3, 5, 7, 8, 6]], [[[[18, "level-5"], [19, "level-5", 33554432, [-22, -23, -24], [[5, -19, [0, "2b0ALzXYtJPo0X8C3Xa/1a"], [5, 8559.343, 5661.768244073918]], [6, 0, -20, [0, "79uc5hPb9BjJZW5PetmcIM"], 9], [23, -21, [0, "afZoNH2XZDOKMflhcO3ncB"], [0, 1224.1999999999998, -563.6000000000001], [[[0, -1622.8, 2081.7], [0, -2315.3, 1660.6], [0, -2679.2, 1126.7], [0, -2745.1, 530.8], [0, -2669, -66], [0, -2461.9, -398.5], [0, -2269.8, -588.7], [0, -1817.2, -947.2], [0, -1228.4, -1089.9], [0, -733.1, -988.6], [0, -171.7, -767.8], [0, 284.8, -211.6], [0, 539.3, 502.2], [0, 373.4, -965.5], [0, -2898.5, -1544.2], [0, -3000.8, 3055.2], [0, 1194.3, 1718.9], [0, 533.2, 537.9], [0, 386.1, 1131.4], [0, 77.4, 1621.5], [0, -346.9, 1949.6], [0, -900.1, 2140.6]], 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]]], [24, "48dRzRqnRN2bkSv9uyUwDd", null, -18, 0, [[11, ["healthBar"], -6, [1, ["e9cQsECkREsqrYHJQVVWOE"]]], [11, ["healthBar"], -7, [1, ["e9cQsECkREsqrYHJQVVWOE"]]], [11, ["healthBar"], -8, [1, ["e9cQsECkREsqrYHJQVVWOE"]]], [11, ["healthBar"], -9, [1, ["e9cQsECkREsqrYHJQVVWOE"]]], [12, ["healthBar"], -11, -10, [1, ["e9cQsECkREsqrYHJQVVWOE"]]], [12, ["healthBar"], -13, -12, [1, ["e9cQsECkREsqrYHJQVVWOE"]]], [12, ["healthBar"], -15, -14, [1, ["e9cQsECkREsqrYHJQVVWOE"]]], [12, ["healthBar"], -17, -16, [1, ["e9cQsECkREsqrYHJQVVWOE"]]]], [-1, -2, -3, -4, -5]]], [22, "car-1", 2, 33554432, [-30], [[[5, -25, [0, "febEhcVCJCmanY3zuvkWQl"], [5, 27.8, 60]], [13, true, 1, 0, -26, [0, "94TM66uCBAfYenXbYaR3PP"]], [14, 1, -27, [0, "f8cttexglKcaSmhhKAMkZ2"], [5, 27.8, 60.1]], [6, 0, -28, [0, "82rNUMI5tLPa5/vehZCMSl"], 2], -29], 4, 4, 4, 4, 1], [3, "a0U4DNCadE0r1/TONnWKxX", null, null, null, 1, 0], [1, -610.581, 880.474, 0], [3, 0, 0, -0.7071067811865475, 0.7071067811865476], [1, 0, 0, -90]], [16, "car-2", 2, 33554432, [-36], [[[5, -31, [0, "85OwXYc6FNHpyQKseWyFCb"], [5, 27.8, 60]], [13, true, 1, 0, -32, [0, "98j82JCF1DsaW2LsMjG5dM"]], [14, 1, -33, [0, "00L+B9nGtNa4RJLAvS4V2W"], [5, 27.8, 60.1]], [6, 0, -34, [0, "4cttUISJZABpKvYOz7RLLm"], 4], -35], 4, 4, 4, 4, 1], [3, "70UxTgHRRIgbdZmbJFkqCQ", null, null, null, 1, 0], [1, 609.901, 869.052, 0], [3, 0, 0, 0.7071067811865475, 0.7071067811865476], [1, 1.2, 1.2, 1], [1, 0, 0, 90]], [16, "car-3", 2, 33554432, [-42], [[[5, -37, [0, "558PwHgPFOdKGCb2+HwExW"], [5, 27.8, 60]], [13, true, 1, 0, -38, [0, "13i4+2uKpKr6PqUvnfZePl"]], [14, 1, -39, [0, "549W48hmtGZYtQ/JGQQKsV"], [5, 27.8, 60.1]], [6, 0, -40, [0, "52sVnCxu9GIZUV1v2boA/G"], 6], -41], 4, 4, 4, 4, 1], [3, "7e+qbjVJpPir9JAc3zFrQD", null, null, null, 1, 0], [1, 610.325, 104.225, 0], [3, 0, 0, 0.7071067811865475, 0.7071067811865476], [1, 1.3, 1.3, 1], [1, 0, 0, 90]], [16, "car-4", 2, 33554432, [-48], [[[5, -43, [0, "6b2YnnHy1Ddp1gNC/Y5791"], [5, 27.8, 60]], [13, true, 1, 0, -44, [0, "b34bPOC2xDFK2yI4cKdoLt"]], [14, 1, -45, [0, "31wwnhtHBI048/QmNJxKGx"], [5, 27.8, 60.1]], [6, 0, -46, [0, "91vS3IH8tMAIUgXm5i33u2"], 8], -47], 4, 4, 4, 4, 1], [3, "96fHTOmXFAnKNLW7RvoDaM", null, null, null, 1, 0], [1, -610.157, 132.788, 0], [3, 0, 0, -0.7071067811865475, 0.7071067811865476], [1, 1.2, 1.2, 1], [1, 0, 0, -90]], [20, "cars", 33554432, 1, [2, 3, 4, 5], [[17, -49, [0, "1avgl/AfdFt7Y44zEExOVC"]]], [3, "e4K8OMLjZMcaTYEtzbDeUb", null, null, null, 1, 0], [1, 0, -276.598, 0]], [4, 0, {}, 2, [7, "40tQNkNPtP4aURzO7VMMIR", null, null, -54, [8, "f7TetQCR1Hgpj2RNOtPQqo", 1, [[9, "healthBar", ["_name"], -50], [2, ["_lpos"], -51, [1, -35, 0, 0]], [2, ["_lrot"], -52, [3, 0, 0, 0.7071067811865475, 0.7071067811865476]], [2, ["_euler"], -53, [1, 0, 0, 90]], [10, 50, ["offsetY"], [1, ["88S4hp27BPFpL6l7nafPlR"]]], [15, ["_contentSize"], [1, ["27NHqliW9Fb4bDUyccWJj+"]], [5, 300, 15]], [10, 1, ["_progress"], [1, ["e9cQsECkREsqrYHJQVVWOE"]]]]], 1]], [4, 0, {}, 3, [7, "40tQNkNPtP4aURzO7VMMIR", null, null, -59, [8, "460C7V1GRDkLc9IWZl3Bf6", 1, [[9, "healthBar", ["_name"], -55], [2, ["_lpos"], -56, [1, 35, 0, 0]], [2, ["_lrot"], -57, [3, 0, 0, 0.7071067811865475, 0.7071067811865476]], [2, ["_euler"], -58, [1, 0, 0, 90]], [15, ["_contentSize"], [1, ["27NHqliW9Fb4bDUyccWJj+"]], [5, 300, 15]], [10, 1, ["_progress"], [1, ["e9cQsECkREsqrYHJQVVWOE"]]]]], 3]], [4, 0, {}, 4, [7, "40tQNkNPtP4aURzO7VMMIR", null, null, -64, [8, "b5EGDwIddFsruBGs4i7y6I", 1, [[9, "healthBar", ["_name"], -60], [2, ["_lpos"], -61, [1, 35, 0, 0]], [2, ["_lrot"], -62, [3, 0, 0, 0.7071067811865475, 0.7071067811865476]], [2, ["_euler"], -63, [1, 0, 0, 90]], [15, ["_contentSize"], [1, ["27NHqliW9Fb4bDUyccWJj+"]], [5, 300, 15]], [10, 1, ["_progress"], [1, ["e9cQsECkREsqrYHJQVVWOE"]]]]], 5]], [4, 0, {}, 5, [7, "40tQNkNPtP4aURzO7VMMIR", null, null, -69, [8, "ebln8ot9FEbIV/7Ue8Ucuu", 1, [[9, "healthBar", ["_name"], -65], [2, ["_lpos"], -66, [1, -35, 0, 0]], [2, ["_lrot"], -67, [3, 0, 0, 0.7071067811865475, 0.7071067811865476]], [2, ["_euler"], -68, [1, 0, 0, 90]], [15, ["_contentSize"], [1, ["27NHqliW9Fb4bDUyccWJj+"]], [5, 300, 15]], [10, 1, ["_progress"], [1, ["e9cQsECkREsqrYHJQVVWOE"]]]]], 7]], [1, ["793wXpE0dMZZF5oHFdhgzy"]], [1, ["40tQNkNPtP4aURzO7VMMIR"]], [1, ["40tQNkNPtP4aURzO7VMMIR"]], [1, ["40tQNkNPtP4aURzO7VMMIR"]], [1, ["40tQNkNPtP4aURzO7VMMIR"]], [4, 0, {}, 1, [7, "793wXpE0dMZZF5oHFdhgzy", null, null, -70, [8, "da0Kl7IVBEGIvhTJmMi025", 1, [[9, "PaintRoot", ["_name"], 11], [2, ["_lpos"], 11, [1, -639.97, -359.883, 0]], [2, ["_lrot"], 11, [3, 0, 0, 0, 1]], [2, ["_euler"], 11, [1, 0, 0, 0]]]], 0]], [21, "BulletRoot", 4, 1, [[17, -71, [0, "73OTt9fOxBtblon1GleEvQ"]]], [3, "88dTb61vpDop/QJdLF3Ccr", null, null, null, 1, 0]], [25, 30, 30, 80, 80, 0.1, 3, 2, [0, "c8hi1aIONJQpoPpar0ZX8g"]], [26, 40, 30, 80, 100, 0.1, 2, 3, [0, "beLAG+zO9DqLiZ/wd6PvoZ"]], [27, 30, 30, 100, 80, 0.1, 2, 1, 4, [0, "2aKPnqKLBEXZ/XimfCojso"]], [28, 35, 35, 120, 120, 2, 5, [0, "22t0ZwxIhPK7H4ayFtKFcP"]]], 0, [0, -1, 10, 0, -2, 9, 0, -3, 8, 0, -4, 7, 0, -5, 16, 0, 2, 7, 0, 2, 8, 0, 2, 9, 0, 2, 10, 0, 2, 7, 0, 6, 18, 0, 2, 8, 0, 6, 19, 0, 2, 9, 0, 6, 20, 0, 2, 10, 0, 6, 21, 0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 16, 0, -2, 17, 0, -3, 6, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -5, 18, 0, -1, 7, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -5, 19, 0, -1, 8, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -5, 20, 0, -1, 9, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -5, 21, 0, -1, 10, 0, 0, 6, 0, 1, 12, 0, 1, 12, 0, 1, 12, 0, 1, 12, 0, 3, 7, 0, 1, 13, 0, 1, 13, 0, 1, 13, 0, 1, 13, 0, 3, 8, 0, 1, 14, 0, 1, 14, 0, 1, 14, 0, 1, 14, 0, 3, 9, 0, 1, 15, 0, 1, 15, 0, 1, 15, 0, 1, 15, 0, 3, 10, 0, 3, 16, 0, 0, 17, 0, 13, 1, 2, 7, 6, 3, 7, 6, 4, 7, 6, 5, 7, 6, 71], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 18, 18, 18, 18, 19, 19, 19, 19, 19, 20, 20, 20, 20, 20, 21, 21, 21, 21, 21], [4, 4, 5, 4, 5, 4, 5, 4, 5, 5, 8, 9, 10, 11, 12, 8, 9, 10, 11, 12, 8, 9, 10, 11, 12, 8, 9, 10, 11, 12], [5, 0, 6, 0, 7, 0, 8, 0, 9, 10, 1, 11, 2, 3, 4, 1, 12, 2, 3, 4, 1, 13, 2, 3, 4, 1, 14, 2, 3, 4]], [[{"name": "arena", "rect": {"x": 0, "y": 0, "width": 4000, "height": 2248}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 4000, "height": 2248}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-2000, -1124, 0, 2000, -1124, 0, -2000, 1124, 0, 2000, 1124, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 2248, 4000, 2248, 0, 0, 4000, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -2000, "y": -1124, "z": 0}, "maxPos": {"x": 2000, "y": 1124, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [5], 0, [0], [14], [15]]]]