[1, ["5fpKAgrRJNUZ59V87NwFbK", "86CHtLQoRLkYcH+h9xQG6h@f9941", "ff3ACRArVH6Zy6dSrQ9/Jg", "525Wn55e9KfJgHyymrwV2G", "7f46LBRONF7aOkgekKaZTH", "81lKEvJyRNgqSD3Fi+L3ZL", "0a4no7LTdJeJbqYmu+zkD1", "dec5GXjXRCFb/ngYogPNxm", "9dcxrjryVAf7WISnsGLDdt", "01YiY+IbtIxbbAbZevIyjb@6c48a", "08B4lpzuxG1rZggt15ITjW@6c48a", "08WEhbSwlLXp/E6vy85tyV@6c48a", "0awjJEm0lOZrgmq6QRyaFo@6c48a", "0cL/pExSVCJJTyiVUFvr4v@6c48a", "4dUynpj2dD46OZ4PqaWtX0", "b96V8CSuxMAYNDHGzgNrDG@f9941", "38QUxvzghAw7CgTyTOEDHe@f9941", "d6xdsFp7VMObKfichsMV3P@f9941", "edYR225R5KU7YkhI3UWs4h@f9941", "08WEhbSwlLXp/E6vy85tyV@f9941", "b7HBnQSslNy6oytl/eLEBq", "a4Yof/90tAdaSDCLECf+ct", "35QYurHgpBCKJnzoyMJCLD", "46ZtbII3ZN9L4o1m44O6rO", "46sRigKlhMW5IX+i4qIOyV@f9941", "39eg7OsTdHT4nt3iOt2s82@f9941", "19NQYs6hxDo54hyWTAcezs@6c48a", "24pwTaKGdEbY0aXpIMdeCd@6c48a", "2eY9bbyBRJlJmqIrqHyQe+@6c48a", "76Xaao4HRKO4G3ypcQ+cLT@f9941", "38QUxvzghAw7CgTyTOEDHe@6c48a", "4dTnGVbIRGIY08N/tncyFi@f9941", "46sRigKlhMW5IX+i4qIOyV@6c48a", "4dTnGVbIRGIY08N/tncyFi@6c48a", "7bzlqIK8NFS5ZUlrYuHKKf@f9941", "0egy8lrORML7so3k+5KNar", "9f2QDdIhtPiY8s+6NCQ8g1@f9941", "24pwTaKGdEbY0aXpIMdeCd@f9941", "76Xaao4HRKO4G3ypcQ+cLT@6c48a", "7bOlT0MSVEG6duaggQ3bCY@6c48a", "7bzlqIK8NFS5ZUlrYuHKKf@6c48a", "7cdSnkVxBOlK10Rczz+Eu8@6c48a", "0cL/pExSVCJJTyiVUFvr4v@f9941", "e1B5EMDvhKa461RtKwll8j", "801lteK0lAEJnaaSVvh2rV@6c48a", "86CHtLQoRLkYcH+h9xQG6h@6c48a", "8cw9emvcZIcq+Ie5l5XgnD@6c48a", "91zBdqf65O+ql7U8yxjWP5@6c48a", "9f2QDdIhtPiY8s+6NCQ8g1@6c48a", "a2o+kMk39O+aLa8g1EXSFK@6c48a", "a2o+kMk39O+aLa8g1EXSFK@f9941", "8cw9emvcZIcq+Ie5l5XgnD@f9941", "b96V8CSuxMAYNDHGzgNrDG@6c48a", "d6xdsFp7VMObKfichsMV3P@6c48a", "7bOlT0MSVEG6duaggQ3bCY@f9941", "edYR225R5KU7YkhI3UWs4h@6c48a", "fckx7P6YFIzJmfEy7cpy+m@6c48a", "2eY9bbyBRJlJmqIrqHyQe+@f9941", "12RRXWwB1LJKl47vxH4wxN"], ["node", "_textureSource", "root", "_spriteFrame", "targetInfo", "data", "asset", "target", "source", "_parent", "destroyedSprite", "paintPrefab", "normalBulletPrefab", "dartPrefab", "rocketPrefab", "_defaultClip", "explosionPrefab", "_barSprite"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_layer", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_components", "_children", "_lpos", "_parent", "_lscale"], -1, 4, 9, 2, 5, 1, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "__prefab", "_spriteFrame", "_color"], 1, 1, 4, 6, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.BoxCollider2D", ["_friction", "_sensor", "node", "__prefab", "_size", "_offset"], 1, 1, 4, 5, 5], ["cc.RigidBody2D", ["enabledContactListener", "_type", "bullet", "_gravityScale", "node", "__prefab"], -1, 1, 4], ["cc.Node", ["_name", "_layer", "_mobility", "_components", "_prefab", "_lpos", "_children", "_lrot", "_lscale", "_euler", "_parent"], 0, 12, 4, 5, 2, 5, 5, 5, 1], ["cc.PolygonCollider2D", ["node", "__prefab", "_points", "_offset"], 3, 1, 4, 12, 5], ["91c082Rgh1HBbV2vKQl2J1W", ["maxSpeed", "acceleration", "turnSpeed", "maxHealth", "paintSprayInterval", "weaponType", "fireRate", "node", "__prefab"], -4, 1, 4], ["91ffczWtpJKh6L+Ua3qo7Ip", ["lifeTime", "explosionRadius", "speed", "damage", "bulletType", "node", "__prefab", "explosionPrefab"], -2, 1, 4, 6], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "root", "asset", "targetOverrides", "nestedPrefabInstanceRoots"], 1, 1, 1, 9, 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.TargetOverrideInfo", ["propertyPath", "source", "target", "targetInfo"], 2, 1, 1, 4], ["cc.TargetInfo", ["localID"], 2], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides"], 2, 1, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["cc.Animation", ["playOnLoad", "node", "__prefab", "_clips", "_defaultClip"], 2, 1, 4, 3, 6], ["e9c7fJf5G9M8ZAl2uR3FDyp", ["node", "__prefab"], 3, 1, 4], ["7dd7aydfZBHRbtWPJmh00Xf", ["node", "__prefab"], 3, 1, 4], ["cc.ProgressBar", ["_totalLength", "_progress", "node", "__prefab", "_barSprite"], 1, 1, 4, 1], ["79d6fgSlVRCYoTh/XQ+YRv5", ["node", "__prefab"], 3, 1, 4]], [[11, 0, 2], [13, 0, 1, 2, 3, 4, 5, 5], [16, 0, 2], [3, 0, 1, 2, 1], [19, 0, 1, 2, 2], [10, 0, 2], [2, 0, 2, 3, 4, 2], [4, 2, 3, 5, 4, 1], [1, 2, 3, 8, 4, 3], [1, 0, 1, 5, 4, 3], [1, 0, 1, 5, 4, 7, 3], [14, 0, 1, 2, 3, 4, 5, 4], [17, 0, 1, 2, 2], [18, 0, 1, 2, 3], [20, 0, 1, 2, 3], [6, 0, 2, 1, 6, 3, 4, 5, 7, 8, 9, 4], [4, 0, 2, 3, 4, 2], [15, 0, 1, 2, 3, 2], [5, 0, 1, 3, 4, 5, 4], [21, 0, 1, 2, 2], [22, 0, 1, 2, 3, 4, 2], [23, 0, 1, 1], [7, 0, 1, 3, 2, 1], [3, 0, 1, 1], [2, 2, 3, 4, 1], [7, 0, 1, 2, 1], [5, 0, 2, 4, 5, 3], [8, 0, 1, 2, 3, 4, 6, 5, 7, 8, 8], [8, 0, 1, 2, 3, 4, 5, 7, 8, 7], [9, 2, 3, 4, 0, 1, 5, 6, 7, 6], [1, 0, 1, 6, 5, 4, 7, 3], [1, 0, 1, 8, 6, 5, 4, 7, 3], [1, 0, 1, 8, 5, 4, 3], [1, 0, 1, 6, 5, 4, 9, 3], [1, 0, 1, 5, 4, 9, 3], [6, 0, 1, 10, 3, 4, 5, 3], [3, 0, 1, 2, 3, 1], [2, 1, 0, 2, 3, 5, 4, 3], [2, 1, 0, 2, 3, 5, 3], [4, 1, 2, 3, 5, 4, 2], [12, 0, 1, 2, 3, 4, 5, 3], [5, 0, 2, 1, 4, 5, 4], [24, 0, 1, 1], [9, 0, 1, 5, 6, 7, 3], [25, 0, 1, 2, 3, 4, 3], [26, 0, 1, 1]], [[[{"name": "Effect-4", "rect": {"x": 86, "y": 84, "width": 128, "height": 123}, "offset": {"x": 0, "y": 4.5}, "originalSize": {"width": 300, "height": 300}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-64, -61.5, 0, 64, -61.5, 0, -64, 61.5, 0, 64, 61.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [86, 216, 214, 216, 86, 93, 214, 93], "nuv": [0.2866666666666667, 0.31, 0.7133333333333334, 0.31, 0.2866666666666667, 0.72, 0.7133333333333334, 0.72], "minPos": {"x": -64, "y": -61.5, "z": 0}, "maxPos": {"x": 64, "y": 61.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [9]], [[{"name": "Effect-7", "rect": {"x": 28, "y": 36, "width": 244, "height": 234}, "offset": {"x": 0, "y": -3}, "originalSize": {"width": 300, "height": 300}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-122, -117, 0, 122, -117, 0, -122, 117, 0, 122, 117, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [28, 264, 272, 264, 28, 30, 272, 30], "nuv": [0.09333333333333334, 0.1, 0.9066666666666666, 0.1, 0.09333333333333334, 0.88, 0.9066666666666666, 0.88], "minPos": {"x": -122, "y": -117, "z": 0}, "maxPos": {"x": 122, "y": 117, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [10]], [[{"name": "tennis_court", "rect": {"x": 0, "y": 0, "width": 4000, "height": 2248}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 4000, "height": 2248}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-2000, -1124, 0, 2000, -1124, 0, -2000, 1124, 0, 2000, 1124, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 2248, 4000, 2248, 0, 0, 4000, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -2000, "y": -1124, "z": 0}, "maxPos": {"x": 2000, "y": 1124, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [11]], [[{"name": "Effect-10", "rect": {"x": 0, "y": 0, "width": 300, "height": 299}, "offset": {"x": 0, "y": 0.5}, "originalSize": {"width": 300, "height": 300}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-150, -149.5, 0, 150, -149.5, 0, -150, 149.5, 0, 150, 149.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 300, 300, 300, 0, 1, 300, 1], "nuv": [0, 0.0033333333333333335, 1, 0.0033333333333333335, 0, 1, 1, 1], "minPos": {"x": -150, "y": -149.5, "z": 0}, "maxPos": {"x": 150, "y": 149.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [12]], [[{"name": "missile", "rect": {"x": 0, "y": 0, "width": 2560, "height": 4096}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 2560, "height": 4096}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-1280, -2048, 0, 1280, -2048, 0, -1280, 2048, 0, 1280, 2048, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 4096, 2560, 4096, 0, 0, 2560, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -1280, "y": -2048, "z": 0}, "maxPos": {"x": 1280, "y": 2048, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [13]], [[[5, "level-4"], [30, "level-4", 33554432, [-29, -30, -31], [[3, -15, [0, "2b0ALzXYtJPo0X8C3Xa/1a"], [5, 6125.26, 4051.6897797758784]], [6, 0, -16, [0, "79uc5hPb9BjJZW5PetmcIM"], 9], [7, -17, [0, "7cyAmiMBRE+6g7LbrLDhAi"], [0, 12.5, 1673.8], [5, 2505.9, 694.6]], [7, -18, [0, "90i9i1N2xGr7Y0i102K+Kd"], [0, -22.3, -1392.9], [5, 2529.5, 1257.6]], [7, -19, [0, "38Ke9wT4JLy6aX4lrvtwau"], [0, -2309.1, -4], [5, 1560.7, 4023.9]], [7, -20, [0, "dccW0EEldDp6vuUIynuW0Y"], [0, 1985.1, 2.4], [5, 903.1, 4046]], [22, -21, [0, "74QW2PO45BrrQtoGT5dr+5"], [0, -552.1, -192.8], [[[0, -995.6, -1849.6], [0, -685.5, -1827.5], [0, -731.8, -1198.3], [0, -1000.6, -898.1]], 8, 8, 8, 8]], [25, -22, [0, "6fvdYVnNdFyqCzZZ84WbgG"], [[[0, 1538.5, -1122.4], [0, 1233.9, -1533.2], [0, 1242.3, -2025.9], [0, 1542.1, -2033.6]], 8, 8, 8, 8]], [22, -23, [0, "25aUR3LcNEuL6lsUzY/tmE"], [0, 5, 0], [[[0, -1559.8, 2022.9], [0, -1533.9, 1653.2], [0, -1250.1, 2030.3]], 8, 8, 8]], [22, -24, [0, "fdS/vDY2hPsJS61pqPSceN"], [0, 156.1, 45.6], [[[0, 1078.4, 1981.9], [0, 1384.3, 1613.2], [0, 1382.5, 1987]], 8, 8, 8]], [7, -25, [0, "c9rLWnoHdId7DRPT2lU8YT"], [0, -307.1, -468.2], [5, 142.4, 76.5]], [7, -26, [0, "c4JjAhx9VLtZKqOqR6FInQ"], [0, 0.4, 285.3], [5, 20.2, 1164]], [7, -27, [0, "ebz2UOoWFGeqZ3OVVpw9a4"], [0, 285, -497.1], [5, 131.9, 63.5]], [25, -28, [0, "35l93XldFF9Y8EJzQkF+XT"], [[[0, -116.9, 1001.5], [0, -115, 996.1], [0, -77.7, 938.1], [0, 14, 1025.3], [0, -34.6, 1093.9]], 8, 8, 8, 8, 8]]], [40, "48dRzRqnRN2bkSv9uyUwDd", null, -14, 0, [[17, ["healthBar"], -7, -6, [2, ["e9cQsECkREsqrYHJQVVWOE"]]], [17, ["healthBar"], -9, -8, [2, ["e9cQsECkREsqrYHJQVVWOE"]]], [17, ["healthBar"], -11, -10, [2, ["e9cQsECkREsqrYHJQVVWOE"]]], [17, ["healthBar"], -13, -12, [2, ["e9cQsECkREsqrYHJQVVWOE"]]]], [-1, -2, -3, -4, -5]], [1, 0, 173.677, 0]], [15, "car-1", 2, 33554432, [-37], [[[3, -32, [0, "febEhcVCJCmanY3zuvkWQl"], [5, 27.8, 60]], [18, true, 1, 0, -33, [0, "94TM66uCBAfYenXbYaR3PP"]], [16, 1, -34, [0, "f8cttexglKcaSmhhKAMkZ2"], [5, 27.8, 60.1]], [6, 0, -35, [0, "82rNUMI5tLPa5/vehZCMSl"], 2], -36], 4, 4, 4, 4, 1], [1, "a0U4DNCadE0r1/TONnWKxX", null, null, null, 1, 0], [1, -610.581, 880.474, 0], [3, 0, 0, -0.7071067811865475, 0.7071067811865476], [1, 1.2, 1.2, 1], [1, 0, 0, -90]], [15, "car-2", 2, 33554432, [-43], [[[3, -38, [0, "85OwXYc6FNHpyQKseWyFCb"], [5, 27.8, 60]], [18, true, 1, 0, -39, [0, "98j82JCF1DsaW2LsMjG5dM"]], [16, 1, -40, [0, "00L+B9nGtNa4RJLAvS4V2W"], [5, 27.8, 60.1]], [6, 0, -41, [0, "4cttUISJZABpKvYOz7RLLm"], 4], -42], 4, 4, 4, 4, 1], [1, "70UxTgHRRIgbdZmbJFkqCQ", null, null, null, 1, 0], [1, 609.901, 869.052, 0], [3, 0, 0, 0.7071067811865475, 0.7071067811865476], [1, 1.2, 1.2, 1], [1, 0, 0, 90]], [15, "car-3", 2, 33554432, [-49], [[[3, -44, [0, "558PwHgPFOdKGCb2+HwExW"], [5, 27.8, 60]], [18, true, 1, 0, -45, [0, "13i4+2uKpKr6PqUvnfZePl"]], [16, 1, -46, [0, "549W48hmtGZYtQ/JGQQKsV"], [5, 27.8, 60.1]], [6, 0, -47, [0, "52sVnCxu9GIZUV1v2boA/G"], 6], -48], 4, 4, 4, 4, 1], [1, "7e+qbjVJpPir9JAc3zFrQD", null, null, null, 1, 0], [1, 610.325, 104.225, 0], [3, 0, 0, 0.7071067811865475, 0.7071067811865476], [1, 1.3, 1.3, 1], [1, 0, 0, 90]], [15, "car-4", 2, 33554432, [-55], [[[3, -50, [0, "6b2YnnHy1Ddp1gNC/Y5791"], [5, 27.8, 60]], [18, true, 1, 0, -51, [0, "b34bPOC2xDFK2yI4cKdoLt"]], [16, 1, -52, [0, "31wwnhtHBI048/QmNJxKGx"], [5, 27.8, 60.1]], [6, 0, -53, [0, "91vS3IH8tMAIUgXm5i33u2"], 8], -54], 4, 4, 4, 4, 1], [1, "96fHTOmXFAnKNLW7RvoDaM", null, null, null, 1, 0], [1, -610.157, 132.788, 0], [3, 0, 0, -0.7071067811865475, 0.7071067811865476], [1, 1.2, 1.2, 1], [1, 0, 0, -90]], [31, "cars", 33554432, 1, [2, 3, 4, 5], [[23, -56, [0, "1avgl/AfdFt7Y44zEExOVC"]]], [1, "e4K8OMLjZMcaTYEtzbDeUb", null, null, null, 1, 0], [1, 0, -276.598, 0]], [2, ["793wXpE0dMZZF5oHFdhgzy"]], [8, 0, {}, 2, [11, "40tQNkNPtP4aURzO7VMMIR", null, null, -61, [12, "f7TetQCR1Hgpj2RNOtPQqo", 1, [[13, "healthBar", ["_name"], -57], [4, ["_lpos"], -58, [1, -35, 0, 0]], [4, ["_lrot"], -59, [3, 0, 0, 0.7071067811865475, 0.7071067811865476]], [4, ["_euler"], -60, [1, 0, 0, 90]], [14, 50, ["offsetY"], [2, ["88S4hp27BPFpL6l7nafPlR"]]], [19, ["_contentSize"], [2, ["27NHqliW9Fb4bDUyccWJj+"]], [5, 300, 15]], [14, 1, ["_progress"], [2, ["e9cQsECkREsqrYHJQVVWOE"]]]]], 1]], [2, ["40tQNkNPtP4aURzO7VMMIR"]], [8, 0, {}, 3, [11, "40tQNkNPtP4aURzO7VMMIR", null, null, -66, [12, "460C7V1GRDkLc9IWZl3Bf6", 1, [[13, "healthBar", ["_name"], -62], [4, ["_lpos"], -63, [1, 35, 0, 0]], [4, ["_lrot"], -64, [3, 0, 0, 0.7071067811865475, 0.7071067811865476]], [4, ["_euler"], -65, [1, 0, 0, 90]], [19, ["_contentSize"], [2, ["27NHqliW9Fb4bDUyccWJj+"]], [5, 300, 15]], [14, 1, ["_progress"], [2, ["e9cQsECkREsqrYHJQVVWOE"]]]]], 3]], [2, ["40tQNkNPtP4aURzO7VMMIR"]], [8, 0, {}, 4, [11, "40tQNkNPtP4aURzO7VMMIR", null, null, -71, [12, "b5EGDwIddFsruBGs4i7y6I", 1, [[13, "healthBar", ["_name"], -67], [4, ["_lpos"], -68, [1, 35, 0, 0]], [4, ["_lrot"], -69, [3, 0, 0, 0.7071067811865475, 0.7071067811865476]], [4, ["_euler"], -70, [1, 0, 0, 90]], [19, ["_contentSize"], [2, ["27NHqliW9Fb4bDUyccWJj+"]], [5, 300, 15]], [14, 1, ["_progress"], [2, ["e9cQsECkREsqrYHJQVVWOE"]]]]], 5]], [2, ["40tQNkNPtP4aURzO7VMMIR"]], [8, 0, {}, 5, [11, "40tQNkNPtP4aURzO7VMMIR", null, null, -76, [12, "ebln8ot9FEbIV/7Ue8Ucuu", 1, [[13, "healthBar", ["_name"], -72], [4, ["_lpos"], -73, [1, -35, 0, 0]], [4, ["_lrot"], -74, [3, 0, 0, 0.7071067811865475, 0.7071067811865476]], [4, ["_euler"], -75, [1, 0, 0, 90]], [19, ["_contentSize"], [2, ["27NHqliW9Fb4bDUyccWJj+"]], [5, 300, 15]], [14, 1, ["_progress"], [2, ["e9cQsECkREsqrYHJQVVWOE"]]]]], 7]], [2, ["40tQNkNPtP4aURzO7VMMIR"]], [8, 0, {}, 1, [11, "793wXpE0dMZZF5oHFdhgzy", null, null, -77, [12, "da0Kl7IVBEGIvhTJmMi025", 1, [[13, "PaintRoot", ["_name"], 7], [4, ["_lpos"], 7, [1, -639.97, -359.883, 0]], [4, ["_lrot"], 7, [3, 0, 0, 0, 1]], [4, ["_euler"], 7, [1, 0, 0, 0]]]], 0]], [32, "BulletRoot", 4, 1, [[23, -78, [0, "73OTt9fOxBtblon1GleEvQ"]]], [1, "88dTb61vpDop/QJdLF3Ccr", null, null, null, 1, 0]], [27, 30, 30, 70, 70, 0.1, 1, 2, 2, [0, "c8hi1aIONJQpoPpar0ZX8g"]], [27, 30, 30, 100, 100, 0.1, 1, 2, 3, [0, "beLAG+zO9DqLiZ/wd6PvoZ"]], [28, 30, 30, 100, 100, 0.1, 1, 4, [0, "2aKPnqKLBEXZ/XimfCojso"]], [28, 30, 30, 120, 120, 0.1, 1, 5, [0, "22t0ZwxIhPK7H4ayFtKFcP"]]], 0, [0, -1, 14, 0, -2, 12, 0, -3, 10, 0, -4, 8, 0, -5, 16, 0, 7, 8, 0, 8, 18, 0, 7, 10, 0, 8, 19, 0, 7, 12, 0, 8, 20, 0, 7, 14, 0, 8, 21, 0, 2, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 16, 0, -2, 17, 0, -3, 6, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -5, 18, 0, -1, 8, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -5, 19, 0, -1, 10, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -5, 20, 0, -1, 12, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -5, 21, 0, -1, 14, 0, 0, 6, 0, 4, 9, 0, 4, 9, 0, 4, 9, 0, 4, 9, 0, 2, 8, 0, 4, 11, 0, 4, 11, 0, 4, 11, 0, 4, 11, 0, 2, 10, 0, 4, 13, 0, 4, 13, 0, 4, 13, 0, 4, 13, 0, 2, 12, 0, 4, 15, 0, 4, 15, 0, 4, 15, 0, 4, 15, 0, 2, 14, 0, 2, 16, 0, 0, 17, 0, 5, 1, 2, 9, 6, 3, 9, 6, 4, 9, 6, 5, 9, 6, 78], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 18, 18, 18, 18, 19, 19, 19, 19, 19, 20, 20, 20, 20, 20, 21, 21, 21, 21, 21], [6, 6, 3, 6, 3, 6, 3, 6, 3, 3, 10, 11, 12, 13, 14, 10, 11, 12, 13, 14, 10, 11, 12, 13, 14, 10, 11, 12, 13, 14], [14, 0, 15, 0, 16, 0, 17, 0, 18, 19, 1, 20, 2, 3, 4, 1, 21, 2, 3, 4, 1, 22, 2, 3, 4, 1, 23, 2, 3, 4]], [[[5, "dartexplosion"], [9, "dartexplosion", 4, [[3, -2, [0, "2d8Uo8TgxJ8o6pNZyHFFet"], [5, 70, 70]], [6, 0, -3, [0, "feZKVEbhxHiqBlCm2AJ44X"], 0], [20, true, -4, [0, "3fQSe0A4xA24P+gEF7OuS+"], [1], 2]], [1, "efWs+U9/hPSrpE3lIMbNTn", null, null, null, -1, 0]]], 0, [0, 2, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 5, 1, 4], [0, 0, 0], [3, -1, 15], [24, 5, 5]], [[[5, "bulletexplosion"], [9, "bulletexplosion", 4, [[3, -2, [0, "2d8Uo8TgxJ8o6pNZyHFFet"], [5, 77, 74]], [24, -3, [0, "feZKVEbhxHiqBlCm2AJ44X"], 0], [20, true, -4, [0, "3ashz75jBAirolN6A7kHz/"], [1], 2]], [1, "efWs+U9/hPSrpE3lIMbNTn", null, null, null, -1, 0]]], 0, [0, 2, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 5, 1, 4], [0, 0, 0], [3, -1, 15], [25, 6, 6]], [[{"name": "Effect-9", "rect": {"x": 0, "y": 0, "width": 300, "height": 294}, "offset": {"x": 0, "y": 3}, "originalSize": {"width": 300, "height": 300}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-150, -147, 0, 150, -147, 0, -150, 147, 0, 150, 147, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 300, 300, 300, 0, 6, 300, 6], "nuv": [0, 0.02, 1, 0.02, 0, 1, 1, 1], "minPos": {"x": -150, "y": -147, "z": 0}, "maxPos": {"x": 150, "y": 147, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [26]], [[{"name": "default_progressbar", "rect": {"x": 0, "y": 0, "width": 30, "height": 15}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 30, "height": 15}, "rotated": false, "capInsets": [10, 4, 10, 4], "vertices": {"rawPosition": [-15, -7.5, 0, 15, -7.5, 0, -15, 7.5, 0, 15, 7.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 15, 30, 15, 0, 0, 30, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -15, "y": -7.5, "z": 0}, "maxPos": {"x": 15, "y": 7.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [27]], [[{"name": "bullet", "rect": {"x": 59, "y": 9, "width": 82, "height": 182}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 200, "height": 200}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-41, -91, 0, 41, -91, 0, -41, 91, 0, 41, 91, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [59, 191, 141, 191, 59, 9, 141, 9], "nuv": [0.295, 0.045, 0.705, 0.045, 0.295, 0.955, 0.705, 0.955], "minPos": {"x": -41, "y": -91, "z": 0}, "maxPos": {"x": 41, "y": 91, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [28]], [[[5, "paint_pink"], [10, "paint_pink", 524288, [[3, -2, [0, "c9MFEcCXNCw5OyUhbFvKIv"], [5, 40, 40]], [6, 0, -3, [0, "3bDUM1MJhMNqGAxPNOG1JO"], 0], [21, -4, [0, "d1tExh7/xKNL6/QviQUtNg"]]], [1, "59FI50yHlHrL2PqE45iCqF", null, null, null, -1, 0], [1, 264.546, -172.776, 1000]]], 0, [0, 2, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 5, 1, 4], [0], [3], [29]], [[{"name": "rx7-t", "rect": {"x": 0, "y": 0, "width": 204, "height": 489}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 204, "height": 489}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-102, -244.5, 0, 102, -244.5, 0, -102, 244.5, 0, 102, 244.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 489, 204, 489, 0, 0, 204, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -102, "y": -244.5, "z": 0}, "maxPos": {"x": 102, "y": 244.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [30]], [[[5, "paint_yellow"], [10, "paint_yellow", 524288, [[3, -2, [0, "c9MFEcCXNCw5OyUhbFvKIv"], [5, 40, 40]], [6, 0, -3, [0, "3bDUM1MJhMNqGAxPNOG1JO"], 0], [21, -4, [0, "d1tExh7/xKNL6/QviQUtNg"]]], [1, "59FI50yHlHrL2PqE45iCqF", null, null, null, -1, 0], [1, 264.546, -172.776, 1000]]], 0, [0, 2, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 5, 1, 4], [0], [3], [31]], [[{"name": "dartexplosion", "rect": {"x": 0, "y": 0, "width": 1000, "height": 984}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 1000, "height": 984}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-500, -492, 0, 500, -492, 0, -500, 492, 0, 500, 492, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 984, 1000, 984, 0, 0, 1000, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -500, "y": -492, "z": 0}, "maxPos": {"x": 500, "y": 492, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [32]], [[{"name": "paint_yellow", "rect": {"x": 0, "y": 0, "width": 733, "height": 553}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 733, "height": 553}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-366.5, -276.5, 0, 366.5, -276.5, 0, -366.5, 276.5, 0, 366.5, 276.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 553, 733, 553, 0, 0, 733, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -366.5, "y": -276.5, "z": 0}, "maxPos": {"x": 366.5, "y": 276.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [33]], [[[5, "PaintRoot"], [10, "PaintRoot", 33554432, [[42, -2, [0, "42wNdcN3ZN8YHmmGI5sokS"]]], [1, "793wXpE0dMZZF5oHFdhgzy", null, null, null, -1, 0], [1, -639.97, -359.883, 0]]], 0, [0, 2, 1, 0, 0, 1, 0, 5, 1, 2], [], [], []], [[[5, "dart"], [9, "dart", 4, [[3, -2, [0, "9cbtBk4jRNX6ed0WVOqeL1"], [5, 50, 50]], [6, 0, -3, [0, "a3mwrWJdRLLq6lcvJ4muo0"], 0], [29, 20, 4, 1, 2, 0, -4, [0, "dd+CZkI+xB9pPwMl6qkkM+"], 1], [26, true, true, -5, [0, "1aa1pr+ylBTY7CvLmOqrpP"]], [39, true, -6, [0, "e3oeF8241IFZ22eomt2a41"], [0, 0.2, 0.1], [5, 50.2, 49.9]], [20, true, -7, [0, "45zG9bAVtMsIZH2/CAz2j2"], [2], 3]], [1, "81/HPjmJ1Mz7rPzVFz0YOJ", null, null, null, -1, 0]]], 0, [0, 2, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 5, 1, 7], [0, 0, 0, 0], [3, 16, -1, 15], [34, 35, 7, 7]], [[[5, "healthBar"], [33, "healthBar", 33554432, [-7], [[3, -2, [0, "b9VSg4lIBD2674EWNr0cu8"], [5, 300, 15]], [37, 1, 0, -3, [0, "cdITcchadObYwtHhZvTDcL"], [4, **********], 0], [44, 300, 0.5, -5, [0, "e9cQsECkREsqrYHJQVVWOE"], -4], [45, -6, [0, "88S4hp27BPFpL6l7nafPlR"]]], [1, "40tQNkNPtP4aURzO7VMMIR", null, null, null, -1, 0], [1, 0.2, 0.3, 1]], [35, "Bar", 33554432, 1, [[[36, -8, [0, "27NHqliW9Fb4bDUyccWJj+"], [5, 150, 15], [0, 0, 0.5]], -9], 4, 1], [1, "5cYVKvVKJIe6PUw2kXTDSC", null, null, null, 1, 0], [1, -150, 0, 0]], [38, 1, 0, 2, [0, "e4T3FcLEZKR5ceOsPb+eF1"], [4, **********]]], 0, [0, 2, 1, 0, 0, 1, 0, 0, 1, 0, 17, 3, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, -2, 3, 0, 5, 1, 9], [0, 3], [3, 3], [36, 37]], [[{"name": "paint_pink", "rect": {"x": 0, "y": 0, "width": 761, "height": 556}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 761, "height": 556}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-380.5, -278, 0, 380.5, -278, 0, -380.5, 278, 0, 380.5, 278, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 556, 761, 556, 0, 0, 761, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -380.5, "y": -278, "z": 0}, "maxPos": {"x": 380.5, "y": 278, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [38]], [[{"name": "Effect-3", "rect": {"x": 111, "y": 107, "width": 69, "height": 74}, "offset": {"x": -4.5, "y": 6}, "originalSize": {"width": 300, "height": 300}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-34.5, -37, 0, 34.5, -37, 0, -34.5, 37, 0, 34.5, 37, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [111, 193, 180, 193, 111, 119, 180, 119], "nuv": [0.37, 0.39666666666666667, 0.6, 0.39666666666666667, 0.37, 0.6433333333333333, 0.6, 0.6433333333333333], "minPos": {"x": -34.5, "y": -37, "z": 0}, "maxPos": {"x": 34.5, "y": 37, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [39]], [[{"name": "dart", "rect": {"x": 0, "y": 0, "width": 1130, "height": 1186}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 1130, "height": 1186}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-565, -593, 0, 565, -593, 0, -565, 593, 0, 565, 593, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 1186, 1130, 1186, 0, 0, 1130, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -565, "y": -593, "z": 0}, "maxPos": {"x": 565, "y": 593, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [40]], [[{"name": "Effect-6", "rect": {"x": 58, "y": 45, "width": 207, "height": 210}, "offset": {"x": 11.5, "y": 0}, "originalSize": {"width": 300, "height": 300}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-103.5, -105, 0, 103.5, -105, 0, -103.5, 105, 0, 103.5, 105, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [58, 255, 265, 255, 58, 45, 265, 45], "nuv": [0.19333333333333333, 0.15, 0.8833333333333333, 0.15, 0.19333333333333333, 0.85, 0.8833333333333333, 0.85], "minPos": {"x": -103.5, "y": -105, "z": 0}, "maxPos": {"x": 103.5, "y": 105, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [41]], [[[5, "rocket"], [9, "rocket", 4, [[3, -2, [0, "9cbtBk4jRNX6ed0WVOqeL1"], [5, 30, 50]], [6, 0, -3, [0, "a3mwrWJdRLLq6lcvJ4muo0"], 0], [29, 20, 15, 2, 0.8, 200, -4, [0, "dd+CZkI+xB9pPwMl6qkkM+"], 1], [41, true, true, 1, -5, [0, "1aa1pr+ylBTY7CvLmOqrpP"]], [7, -6, [0, "e3oeF8241IFZ22eomt2a41"], [0, -0.2, 0.7], [5, 25.8, 44.7]]], [1, "81/HPjmJ1Mz7rPzVFz0YOJ", null, null, null, -1, 0]]], 0, [0, 2, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 5, 1, 6], [0, 0], [3, 16], [42, 43]], [[{"name": "Effect-11", "rect": {"x": 8, "y": 20, "width": 287, "height": 279}, "offset": {"x": 1.5, "y": -9.5}, "originalSize": {"width": 300, "height": 300}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-143.5, -139.5, 0, 143.5, -139.5, 0, -143.5, 139.5, 0, 143.5, 139.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [8, 280, 295, 280, 8, 1, 295, 1], "nuv": [0.02666666666666667, 0.0033333333333333335, 0.9833333333333333, 0.0033333333333333335, 0.02666666666666667, 0.9333333333333333, 0.9833333333333333, 0.9333333333333333], "minPos": {"x": -143.5, "y": -139.5, "z": 0}, "maxPos": {"x": 143.5, "y": 139.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [44]], [[{"name": "crash", "rect": {"x": 0, "y": 0, "width": 837, "height": 705}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 837, "height": 705}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-418.5, -352.5, 0, 418.5, -352.5, 0, -418.5, 352.5, 0, 418.5, 352.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 705, 837, 705, 0, 0, 837, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -418.5, "y": -352.5, "z": 0}, "maxPos": {"x": 418.5, "y": 352.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [45]], [[{"name": "paint_orange", "rect": {"x": 0, "y": 0, "width": 862, "height": 677}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 862, "height": 677}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-431, -338.5, 0, 431, -338.5, 0, -431, 338.5, 0, 431, 338.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 677, 862, 677, 0, 0, 862, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -431, "y": -338.5, "z": 0}, "maxPos": {"x": 431, "y": 338.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [46]], [[{"name": "Effect-8", "rect": {"x": 10, "y": 21, "width": 280, "height": 267}, "offset": {"x": 0, "y": -4.5}, "originalSize": {"width": 300, "height": 300}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-140, -133.5, 0, 140, -133.5, 0, -140, 133.5, 0, 140, 133.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [10, 279, 290, 279, 10, 12, 290, 12], "nuv": [0.03333333333333333, 0.04, 0.9666666666666667, 0.04, 0.03333333333333333, 0.93, 0.9666666666666667, 0.93], "minPos": {"x": -140, "y": -133.5, "z": 0}, "maxPos": {"x": 140, "y": 133.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [47]], [[{"name": "default_progressbar_bg", "rect": {"x": 0, "y": 0, "width": 60, "height": 15}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 60, "height": 15}, "rotated": false, "capInsets": [10, 4, 10, 4], "vertices": {"rawPosition": [-30, -7.5, 0, 30, -7.5, 0, -30, 7.5, 0, 30, 7.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 15, 60, 15, 0, 0, 60, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -30, "y": -7.5, "z": 0}, "maxPos": {"x": 30, "y": 7.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [48]], [[{"name": "paint_purple", "rect": {"x": 0, "y": 0, "width": 836, "height": 642}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 836, "height": 642}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-418, -321, 0, 418, -321, 0, -418, 321, 0, 418, 321, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 642, 836, 642, 0, 0, 836, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -418, "y": -321, "z": 0}, "maxPos": {"x": 418, "y": 321, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [49]], [[[5, "paint_purple"], [10, "paint_purple", 524288, [[3, -2, [0, "c9MFEcCXNCw5OyUhbFvKIv"], [5, 40, 40]], [6, 0, -3, [0, "3bDUM1MJhMNqGAxPNOG1JO"], 0], [21, -4, [0, "d1tExh7/xKNL6/QviQUtNg"]]], [1, "59FI50yHlHrL2PqE45iCqF", null, null, null, -1, 0], [1, 264.546, -172.776, 1000]]], 0, [0, 2, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 5, 1, 4], [0], [3], [50]], [[[5, "paint_orange"], [10, "paint_orange", 524288, [[3, -2, [0, "c9MFEcCXNCw5OyUhbFvKIv"], [5, 40, 40]], [6, 0, -3, [0, "3bDUM1MJhMNqGAxPNOG1JO"], 0], [21, -4, [0, "d1tExh7/xKNL6/QviQUtNg"]]], [1, "59FI50yHlHrL2PqE45iCqF", null, null, null, -1, 0], [1, 264.546, -172.776, 1000]]], 0, [0, 2, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 5, 1, 4], [0], [3], [51]], [[{"name": "z370-t", "rect": {"x": 0, "y": 0, "width": 195, "height": 410}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 195, "height": 410}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-97.5, -205, 0, 97.5, -205, 0, -97.5, 205, 0, 97.5, 205, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 410, 195, 410, 0, 0, 195, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -97.5, "y": -205, "z": 0}, "maxPos": {"x": 97.5, "y": 205, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [52]], [[{"name": "taycan-t", "rect": {"x": 0, "y": 0, "width": 607, "height": 1360}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 607, "height": 1360}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-303.5, -680, 0, 303.5, -680, 0, -303.5, 680, 0, 303.5, 680, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 1360, 607, 1360, 0, 0, 607, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -303.5, "y": -680, "z": 0}, "maxPos": {"x": 303.5, "y": 680, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [53]], [[[5, "explosion"], [34, "explosion", 4, [[3, -2, [0, "62WFa1tSNHxJR2R5nekKGK"], [5, 69, 74]], [24, -3, [0, "77LMhRgHVJGrFVaAB6J+E/"], 0], [20, true, -4, [0, "66zAs+h6pEwbPj7KW9xhDn"], [1], 2]], [1, "82jd6+kkpDPp91NwescCDV", null, null, null, -1, 0], [1, 0.30000001192092896, 0.30000001192092896, 1]]], 0, [0, 2, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 5, 1, 4], [0, 0, 0], [3, -1, 15], [54, 8, 8]], [[{"name": "86-t", "rect": {"x": 0, "y": 0, "width": 204, "height": 428}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 204, "height": 428}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-102, -214, 0, 102, -214, 0, -102, 214, 0, 102, 214, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 428, 204, 428, 0, 0, 204, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -102, "y": -214, "z": 0}, "maxPos": {"x": 102, "y": 214, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [55]], [[{"name": "Effect-5", "rect": {"x": 79, "y": 72, "width": 183, "height": 155}, "offset": {"x": 20.5, "y": 0.5}, "originalSize": {"width": 300, "height": 300}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-91.5, -77.5, 0, 91.5, -77.5, 0, -91.5, 77.5, 0, 91.5, 77.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [79, 228, 262, 228, 79, 73, 262, 73], "nuv": [0.2633333333333333, 0.24333333333333335, 0.8733333333333333, 0.24333333333333335, 0.2633333333333333, 0.76, 0.8733333333333333, 0.76], "minPos": {"x": -91.5, "y": -77.5, "z": 0}, "maxPos": {"x": 91.5, "y": 77.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [56]], [[[5, "bullet"], [9, "bullet", 4, [[3, -2, [0, "9cbtBk4jRNX6ed0WVOqeL1"], [5, 15, 30]], [6, 0, -3, [0, "a3mwrWJdRLLq6lcvJ4muo0"], 0], [43, 1, 0, -4, [0, "dd+CZkI+xB9pPwMl6qkkM+"], 1], [26, true, true, -5, [0, "1aa1pr+ylBTY7CvLmOqrpP"]], [7, -6, [0, "e3oeF8241IFZ22eomt2a41"], [0, -0.1, 0], [5, 14.9, 30.2]]], [1, "81/HPjmJ1Mz7rPzVFz0YOJ", null, null, null, -1, 0]]], 0, [0, 2, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 5, 1, 6], [0, 0], [3, 16], [57, 58]]]]