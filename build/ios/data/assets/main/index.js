System.register("chunks:///_virtual/AIController.ts",["./rollupPluginModLoBabelHelpers.js","cc","./GameManager.ts"],(function(e){var t,n,s,i,r,a,o;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.initializerDefineProperty},function(e){s=e.cclegacy,i=e._decorator,r=e.Component,a=e.Vec2},function(e){o=e.GameManager}],execute:function(){var l,h,u,d,c,y;s._RF.push({},"f2409Jfl1NKgaxzoqghBXWi","AIController",void 0);const{ccclass:g,property:T}=i;var b=function(e){return e[e.FreeDrive=0]="FreeDrive",e[e.BoundaryTurning=1]="BoundaryTurning",e}(b||{});e("AIController",g("AIController")((u=t((h=class extends r{constructor(...e){super(...e),n(this,"sceneMinX",u,this),n(this,"sceneMaxX",d,this),n(this,"sceneMinY",c,this),n(this,"sceneMaxY",y,this),this.aiStates=[],this.canvasWorldPos=new a,this.boundaryTurnTimers=[],this.boundaryTurnTargetAngles=[],this.boundaryTurnDirections=[],this.boundaryTurnStartAngles=[],this.lastAngleChanges=[],this.boundaryTurnCooldowns=[],this.aiPlayers=[]}start(){}onScenePrefabLoaded(){console.log("场景预制体加载完成，通过GameManager获取AI车辆列表..."),this.aiPlayers=o.getInstance().getAIPlayers(),this.aiStates=this.aiPlayers.map((()=>b.FreeDrive)),this.boundaryTurnTimers=this.aiPlayers.map((()=>0)),this.boundaryTurnTargetAngles=this.aiPlayers.map((()=>0)),this.boundaryTurnDirections=this.aiPlayers.map((()=>0)),this.boundaryTurnStartAngles=this.aiPlayers.map((()=>0)),this.lastAngleChanges=this.aiPlayers.map((()=>0)),this.boundaryTurnCooldowns=this.aiPlayers.map((()=>0)),console.log(`AI车辆查找完成，找到 ${this.aiPlayers.length} 个AI车辆`)}refreshAIPlayers(){this.aiPlayers=o.getInstance().getAIPlayers(),this.aiStates=this.aiPlayers.map((()=>b.FreeDrive)),this.boundaryTurnTimers=this.aiPlayers.map((()=>0)),this.boundaryTurnTargetAngles=this.aiPlayers.map((()=>0)),this.boundaryTurnDirections=this.aiPlayers.map((()=>0)),this.boundaryTurnStartAngles=this.aiPlayers.map((()=>0)),this.lastAngleChanges=this.aiPlayers.map((()=>0)),this.boundaryTurnCooldowns=this.aiPlayers.map((()=>0)),console.log(`AI车辆列表已刷新，当前共有 ${this.aiPlayers.length} 个AI车辆`)}update(e){for(let t=0;t<this.aiPlayers.length;t++){const n=this.aiPlayers[t];n&&(this.boundaryTurnTimers[t]+=e,this.boundaryTurnCooldowns[t]+=e,this.aiStates[t]!==b.BoundaryTurning?0!=this.aiPlayers.length&&this.freeDrive(n,t):this.handleBoundaryTurning(n,t,e))}}freeDrive(e,t){Math.random()<.005&&e.setDirection(Math.random()<.5?-1:1),Math.random()<.01&&e.setAccel(Math.random()<.8?1:0);const n=e.node.worldPosition,s=n.x-this.canvasWorldPos.x,i=n.y-this.canvasWorldPos.y,r=400,a=s<this.sceneMinX+r,o=s>this.sceneMaxX-r,l=i<this.sceneMinY+r,h=i>this.sceneMaxY-r;if(a||o||l||h){const n=this.boundaryTurnCooldowns[t]<0?Math.abs(this.boundaryTurnCooldowns[t]):3;if(this.boundaryTurnCooldowns[t]>=n)this.startBoundaryTurning(e,t,a,o,l,h);else{this.boundaryTurnCooldowns[t]<0&&Math.abs(this.boundaryTurnCooldowns[t]);e.setAccel(1)}}else e.setAccel(1)}startBoundaryTurning(e,t,n,s,i,r){this.aiStates[t]=b.BoundaryTurning,this.boundaryTurnTimers[t]=0,this.boundaryTurnStartAngles[t]=e.getCurrentAngle(),this.boundaryTurnCooldowns[t]=0;let a=0,o=0;const l=e.getCurrentAngle();n&&i||n&&r||s&&i||s&&r?(a=(l+180)%360,this.boundaryTurnCooldowns[t]=-5):n?a=l>180?90*Math.random():270+90*Math.random():s?a=l<180?180+180*Math.random():180*Math.random():i?a=l>90&&l<270?90*Math.random():270+90*Math.random():r&&(a=l<90||l>270?90+180*Math.random():90*Math.random()+270),a%=360,a<0&&(a+=360);let h=a-l;for(;h>180;)h-=360;for(;h<-180;)h+=360;o=h>0?1:-1,this.boundaryTurnTargetAngles[t]=a,this.boundaryTurnDirections[t]=o,e.setTargetAngle(a),e.setDirection(o),e.setAccel(1)}handleBoundaryTurning(e,t,n){const s=e.getCurrentAngle(),i=this.boundaryTurnTargetAngles[t];let r=s-this.boundaryTurnStartAngles[t];for(;r>180;)r-=360;for(;r<-180;)r+=360;let a=i-s;for(;a>180;)a-=360;for(;a<-180;)a+=360;if(Math.abs(a-this.lastAngleChanges[t])<5&&Math.sign(a)!==Math.sign(this.lastAngleChanges[t]))return e.setDirection(0),e.setAccel(1),this.aiStates[t]=b.FreeDrive,void(this.boundaryTurnCooldowns[t]=0);if(this.lastAngleChanges[t]=a,Math.abs(a)<30)return e.setDirection(0),e.setAccel(1),this.aiStates[t]=b.FreeDrive,void(this.boundaryTurnCooldowns[t]=0);if(Math.abs(r)>=30){const n=e.node.worldPosition,s=n.x-this.canvasWorldPos.x,i=n.y-this.canvasWorldPos.y;if(s>this.sceneMinX+400&&s<this.sceneMaxX-400&&i>this.sceneMinY+400&&i<this.sceneMaxY-400)return e.setDirection(0),e.setAccel(1),this.aiStates[t]=b.FreeDrive,void(this.boundaryTurnCooldowns[t]=0)}if(this.boundaryTurnTimers[t]>3)return e.setDirection(0),e.setAccel(1),void(this.aiStates[t]=b.FreeDrive);const o=Math.abs(a);if(o<30)e.setDirection(0),e.setAccel(1);else{if(o>90)e.setDirection(this.boundaryTurnDirections[t]);else if(o>60)e.setDirection(this.boundaryTurnDirections[t]);else if(o>30){const n=.12,s=.08;this.boundaryTurnTimers[t]%n<s?e.setDirection(this.boundaryTurnDirections[t]):e.setDirection(0)}else e.setDirection(0);e.setAccel(1)}}setAIHealth(e,t){if(e>=0&&e<this.aiPlayers.length){const n=this.aiPlayers[e];n&&n.node&&n.node.isValid&&n.setHealth(t)}}damageAI(e,t){if(e>=0&&e<this.aiPlayers.length){const n=this.aiPlayers[e];n&&n.node&&n.node.isValid&&n.takeDamage(t)}}healAI(e,t){if(e>=0&&e<this.aiPlayers.length){const n=this.aiPlayers[e];n&&n.node&&n.node.isValid&&n.heal(t)}}getAIHealth(e){if(e>=0&&e<this.aiPlayers.length){const t=this.aiPlayers[e];if(t&&t.node&&t.node.isValid)return t.getHealth()}return 0}getAIMaxHealth(e){if(e>=0&&e<this.aiPlayers.length){const t=this.aiPlayers[e];if(t&&t.node&&t.node.isValid)return t.getMaxHealth()}return 0}isAIDead(e){if(e>=0&&e<this.aiPlayers.length){const t=this.aiPlayers[e];if(t&&t.node&&t.node.isValid)return t.isDead()}return!0}isAIBoundaryTurning(e){const t=this.aiPlayers.indexOf(e);return-1!==t&&this.aiStates[t]===b.BoundaryTurning}}).prototype,"sceneMinX",[T],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return-1048.5}}),d=t(h.prototype,"sceneMaxX",[T],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 2328.5}}),c=t(h.prototype,"sceneMinY",[T],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return-692.5}}),y=t(h.prototype,"sceneMaxY",[T],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 1412.5}}),l=h))||l);s._RF.pop()}}}));

System.register("chunks:///_virtual/AIPlayer.ts",["./rollupPluginModLoBabelHelpers.js","cc","./player.ts","./GameManager.ts","./AIController.ts","./Bullet.ts","./SoundManager.ts"],(function(t){var e,i,n,o,r,s,a,l,h,c,d,u,p,y,g,_,f,m,b,P,w,B,A;return{setters:[function(t){e=t.applyDecoratedDescriptor,i=t.initializerDefineProperty},function(t){n=t.cclegacy,o=t.ProgressBar,r=t.SpriteFrame,s=t.Prefab,a=t._decorator,l=t.Component,h=t.Vec2,c=t.RigidBody2D,d=t.ERigidBody2DType,u=t.BoxCollider2D,p=t.Contact2DType,y=t.Layers,g=t.find,_=t.Sprite,f=t.tween,m=t.Vec3},function(t){b=t.player},function(t){P=t.GameManager},function(t){w=t.AIController},function(t){B=t.WeaponType},function(t){A=t.SoundManager}],execute:function(){var D,C,I,S,H,x,v,M,T,V,k,z,F,R,E,N,W,L,O,G,K,$,j,q,J,Q,U,X;n._RF.push({},"91c082Rgh1HBbV2vKQl2J1W","AIPlayer",void 0);const{ccclass:Y,property:Z}=a;t("AIPlayer",(D=Y("AIPlayer"),C=Z(o),I=Z(r),S=Z(s),H=Z({type:s,tooltip:"普通子弹预制体"}),x=Z({type:s,tooltip:"飞镖预制体"}),v=Z({type:s,tooltip:"火箭弹预制体"}),M=Z({tooltip:"射速（发/秒）"}),T=Z({type:B,tooltip:"武器类型"}),D((z=e((k=class extends l{constructor(...t){super(...t),i(this,"maxSpeed",z,this),i(this,"acceleration",F,this),i(this,"brakeDeceleration",R,this),i(this,"turnSpeed",E,this),i(this,"friction",N,this),i(this,"initAngle",W,this),i(this,"maxHealth",L,this),i(this,"healthBar",O,this),i(this,"destroyedSprite",G,this),i(this,"removeDelay",K,this),i(this,"paintPrefab",$,this),i(this,"paintSprayInterval",j,this),i(this,"normalBulletPrefab",q,this),i(this,"dartPrefab",J,this),i(this,"rocketPrefab",Q,this),i(this,"fireRate",U,this),i(this,"weaponType",X,this),this._rigidBody=null,this._direction=0,this._accel=0,this._angle=0,this._targetAngle=0,this._lastValidPosition=new h,this._currentHealth=100,this._isDestroyed=!1,this._paintTimer=0,this._vehicleId="",this._blockCollisionCooldown=0,this._blockCollisionCooldownDuration=3,this._canFire=!0,this._fireTimer=0}onLoad(){this._rigidBody=null,this._direction=0,this._accel=0,this._angle=0,this._targetAngle=0,this._lastValidPosition=new h,this._isDestroyed=!1,this._paintTimer=0,this._vehicleId=`ai_${this.node.name}_${Date.now()}`,this._blockCollisionCooldown=0,this._canFire=!0,this._fireTimer=0}start(){if(this._rigidBody=this.getComponent(c),!this._rigidBody||!this.node||!this.node.isValid)return void console.error("AIPlayer requires RigidBody2D component and valid node");this._rigidBody.type=d.Dynamic,this._rigidBody.allowSleep=!1,this._rigidBody.gravityScale=0,this._rigidBody.linearDamping=.3,this._rigidBody.angularDamping=.9,this._rigidBody.fixedRotation=!0,this._lastValidPosition=new h(this.node.worldPosition.x,this.node.worldPosition.y),this._angle=this.initAngle,this._targetAngle=this.initAngle,this.node.setRotationFromEuler(0,0,this.initAngle),this.initHealthBar();const t=this.getComponent(u);t?(console.log("AIPlayer BoxCollider2D component found"),t.on(p.BEGIN_CONTACT,this.onCollisionEnter,this)):console.error("AIPlayer BoxCollider2D component not found")}initHealthBar(){this._currentHealth=this.maxHealth}updateHealthBar(){this.healthBar&&(this.healthBar.progress=this._currentHealth/this.maxHealth,console.log("AIPlayer updating health bar:",this._currentHealth/this.maxHealth))}update(t){if(!this._rigidBody||!this.node||!this.node.isValid)return;if(this._blockCollisionCooldown>0&&(this._blockCollisionCooldown-=t),this._isDestroyed)return;const e=this._rigidBody.linearVelocity,i=e.length(),n=new h(this.node.worldPosition.x,this.node.worldPosition.y);if(0!==this._direction){const e=this.turnSpeed*t*this._direction;this._targetAngle-=e}const o=this._targetAngle-this._angle;if(Math.abs(o)>.1?this._angle+=.1*o:this._angle=this._targetAngle,this.node.setRotationFromEuler(0,0,this._angle),1===this._accel){const t=(this._angle+90)*Math.PI/180,e=new h(Math.cos(t)*this.acceleration,Math.sin(t)*this.acceleration);this._rigidBody.applyForce(e,n,!0)}else if(-1===this._accel){const t=(this._angle+90)*Math.PI/180,i=new h(Math.cos(t),Math.sin(t));if(e.dot(i)>0){const t=i.clone().multiplyScalar(-this.brakeDeceleration);this._rigidBody.applyForce(t,n,!0)}else{const t=i.clone().multiplyScalar(.5*-this.acceleration);this._rigidBody.applyForce(t,n,!0)}}else if(i>1){const t=e.clone().multiplyScalar(2*-this.friction);this._rigidBody.applyForce(t,n,!0)}if(i>this.maxSpeed){const t=e.clone().normalize();this._rigidBody.linearVelocity=t.multiplyScalar(this.maxSpeed)}if(i<.1){h.distance(n,this._lastValidPosition)>50&&(this.node.setWorldPosition(this._lastValidPosition.x,this._lastValidPosition.y,this.node.worldPosition.z),this._rigidBody.linearVelocity=new h(0,0))}else this._lastValidPosition=n.clone();Math.abs(this._angle)>360&&(this._angle=this._angle%360,this._targetAngle=this._targetAngle%360),this.updatePaintSpray(t),this.updateWeaponSystem(t)}setAccel(t){this._accel=t}setDirection(t){this._direction=t}setTargetAngle(t){this._targetAngle=t}getCurrentAngle(){return this._angle}init(t){this.initAngle=t,this._angle=t,this._targetAngle=t,this.node.setRotationFromEuler(0,0,t)}setHealth(t){this._currentHealth=Math.max(0,Math.min(t,this.maxHealth)),this.updateHealthBar()}takeDamage(t){this._isDestroyed||(console.log("AIPlayer taking damage:",t),this.setHealth(this._currentHealth-t),this.updateHealthBar(),this._currentHealth<=0&&this.destroyVehicle())}heal(t){this.setHealth(this._currentHealth+t),this.updateHealthBar()}getHealth(){return this._currentHealth}getMaxHealth(){return this.maxHealth}isDead(){return this._currentHealth<=0}onCollisionEnter(t,e){if(e.node.layer===y.nameToLayer("Block")){if(this._blockCollisionCooldown>0)return;const t=g("AIController");if(t){const e=t.getComponent(w);if(e){if(e.isAIBoundaryTurning(this))return;this._blockCollisionCooldown=this._blockCollisionCooldownDuration;const t=Math.random()<.5?-1:1,i=130+50*Math.random(),n=this.getCurrentAngle(),o=t>0?n+i:n-i;this.setTargetAngle(o),this.setDirection(t),this.setAccel(1)}}return}const i=e.node.getComponent(b);if(i){console.log("AIPlayer 被玩家车辆撞击");const t=i.getRigidBody();if(t&&!this.isDestroyed){const e=new h(t.linearVelocity.x,t.linearVelocity.y);e.normalize(),e.multiplyScalar(20),this._rigidBody.linearVelocity=e}}}destroyVehicle(){if(!this._isDestroyed){if(this._isDestroyed=!0,console.log("AI车辆被摧毁！"),A.instance.playSoundEffect("carDestruction"),this.destroyedSprite){const t=this.getComponent(_);t&&(t.spriteFrame=this.destroyedSprite)}this.healthBar&&this.healthBar.node&&(this.healthBar.node.active=!1),this.startDestroyAnimation(),this.updateEnemyCount(),this.scheduleRemoveNode()}}updateEnemyCount(){const t=P.getInstance();if(t){const e=t.getAIPlayers().filter((t=>!t.isDestroyed())).length;t.refreshEnemyCount(e)}}scheduleRemoveNode(){this.node&&this.node.isValid&&this.scheduleOnce((()=>{this.removeVehicleNode()}),this.removeDelay)}removeVehicleNode(){if(this.node&&this.node.isValid){console.log("移除AI车辆节点");const t=P.getInstance();if(t){const e=t.getAIPlayers(),i=e.indexOf(this);-1!==i&&e.splice(i,1),t.refreshEnemyCount(e.length)}this.node.removeFromParent()}}startDestroyAnimation(){this.node&&f(this.node).to(2,{scale:new m(1.1,1.1,1)}).start()}isDestroyed(){return this._isDestroyed}getVehicleId(){return this._vehicleId}updatePaintSpray(t){!this._isDestroyed&&this.paintPrefab&&(this._paintTimer+=t,this._paintTimer>=this.paintSprayInterval&&(this.sprayPaint(),this._paintTimer=0))}sprayPaint(){const t=P.getInstance();if(!t)return void console.warn("GameManager未找到，无法喷洒颜料");const e=this.node.getWorldPosition();t.sprayPaint(this.paintPrefab,e,this._vehicleId)}updateWeaponSystem(t){if(this._isDestroyed)return;this._fireTimer+=t;const e=1/this.fireRate;this._fireTimer>=e&&(this._canFire=!0),this.checkAndShoot()}checkAndShoot(){if(!this._canFire)return;const t=P.getInstance();if(!t)return;const e=t.getPlayerComponent();if(!e||!e.node)return;const i=e.node.getWorldPosition(),n=this.node.getWorldPosition(),o=new h(i.x-n.x,i.y-n.y);let r=180*Math.atan2(o.y,o.x)/Math.PI-(this._angle+90);for(;r>180;)r-=360;for(;r<-180;)r+=360;Math.abs(r)<=90&&this.shoot()}shoot(){if(!this._canFire||this._isDestroyed)return;this._canFire=!1,this._fireTimer=0;let t=null;switch(this.weaponType){case B.NORMAL:t=this.normalBulletPrefab;break;case B.DART:t=this.dartPrefab;break;case B.ROCKET:t=this.rocketPrefab}if(!t)return console.warn("AI子弹预制体未设置"),void(this._canFire=!0);const e=(this._angle+90)*Math.PI/180,i=new h(Math.cos(e),Math.sin(e)),n=this.node.worldPosition,o=new m(n.x+50*i.x,n.y+50*i.y,n.z),r=P.getInstance();r&&r.fireBullet(t,o,i,this._vehicleId,this.weaponType),A.instance.playSoundEffect("weaponFire")}}).prototype,"maxSpeed",[Z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 50}}),F=e(k.prototype,"acceleration",[Z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 50}}),R=e(k.prototype,"brakeDeceleration",[Z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 200}}),E=e(k.prototype,"turnSpeed",[Z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 50}}),N=e(k.prototype,"friction",[Z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 1.5}}),W=e(k.prototype,"initAngle",[Z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 0}}),L=e(k.prototype,"maxHealth",[Z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 30}}),O=e(k.prototype,"healthBar",[C],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),G=e(k.prototype,"destroyedSprite",[I],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),K=e(k.prototype,"removeDelay",[Z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 3}}),$=e(k.prototype,"paintPrefab",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),j=e(k.prototype,"paintSprayInterval",[Z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return.2}}),q=e(k.prototype,"normalBulletPrefab",[H],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),J=e(k.prototype,"dartPrefab",[x],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Q=e(k.prototype,"rocketPrefab",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),U=e(k.prototype,"fireRate",[M],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 1.5}}),X=e(k.prototype,"weaponType",[T],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return B.NORMAL}}),V=k))||V));n._RF.pop()}}}));

System.register("chunks:///_virtual/Bullet.ts",["./rollupPluginModLoBabelHelpers.js","cc","./player.ts","./AIPlayer.ts","./SoundManager.ts","./GameManager.ts"],(function(e){var t,i,o,n,s,l,a,r,h,d,c,p,u,y,g,f,m,E,R,x,b;return{setters:[function(e){t=e.applyDecoratedDescriptor,i=e.initializerDefineProperty},function(e){o=e.cclegacy,n=e.Enum,s=e.Prefab,l=e._decorator,a=e.Component,r=e.Vec2,h=e.RigidBody2D,d=e.Collider2D,c=e.Contact2DType,p=e.instantiate,u=e.Vec3,y=e.Animation,g=e.tween,f=e.director,m=e.Director},function(e){E=e.player},function(e){R=e.AIPlayer},function(e){x=e.SoundManager},function(e){b=e.GameManager}],execute:function(){var T,B,_,D,C,P,w,I,O,A,M,V,v;o._RF.push({},"91ffczWtpJKh6L+Ua3qo7Ip","Bullet",void 0);const{ccclass:H,property:k}=l;let N=e("WeaponType",function(e){return e[e.NORMAL=0]="NORMAL",e[e.DART=1]="DART",e[e.ROCKET=2]="ROCKET",e}({})),S=e("BulletType",function(e){return e[e.NORMAL=0]="NORMAL",e[e.DART=1]="DART",e[e.ROCKET=2]="ROCKET",e}({}));n(N),n(S);e("Bullet",(T=H("Bullet"),B=k({type:S,tooltip:"子弹类型"}),_=k({tooltip:"子弹存活时间（秒）"}),D=k({type:s,tooltip:"爆炸效果预制体（火箭弹专用）"}),C=k({tooltip:"爆炸范围（火箭弹专用）"}),T((I=t((w=class e extends a{constructor(...e){super(...e),i(this,"speed",I,this),i(this,"damage",O,this),i(this,"bulletType",A,this),i(this,"lifeTime",M,this),i(this,"explosionPrefab",V,this),i(this,"explosionRadius",v,this),this._shooterId="",this._rigidBody=null,this._direction=new r(0,1),this._velocity=new r(0,0),this._timer=0,this._isExploding=!1,this._pendingDestroy=!1}onLoad(){this._rigidBody=this.getComponent(h),this._rigidBody||console.error("Bullet: RigidBody2D组件未找到");const e=this.getComponent(d);e&&e.on(c.BEGIN_CONTACT,this.onBeginContact,this)}start(){}update(e){if(!this._isExploding){if(this._timer+=e,this._timer>=this.lifeTime)return this.bulletType===S.ROCKET?void this.handleTimeoutExplosion():void this.destroyBullet();this._rigidBody&&(this._velocity.set(this._direction.x*this.speed,this._direction.y*this.speed),this._rigidBody.linearVelocity=this._velocity)}}init(e,t){this._direction=e.normalize(),this._shooterId=t;const i=180*Math.atan2(e.y,e.x)/Math.PI;this.node.setRotationFromEuler(0,0,i-90)}onBeginContact(t,i,o){const n=i.node;if(this.getVehicleId(n)===this._shooterId)return;if(this.bulletType===S.DART){const t=n.getComponent(e);if(t&&t.bulletType===S.DART)return}const s=n.getComponent(E),l=n.getComponent(R);s||l?this.handleVehicleHit(n,s,l):this.handleObstacleHit()}handleVehicleHit(e,t,i){switch(console.log("子弹撞击类型",this.bulletType),this.bulletType){case S.NORMAL:return this.handleNormalBulletHit(t,i),void this.handleBulletExplosion();case S.DART:return this.handleDartHit(t,i),void this.handleDartExplosion();case S.ROCKET:return void this.handleRocketHit(e)}}handleNormalBulletHit(e,t){e?e.takeDamage(this.damage):t&&t.takeDamage(this.damage)}handleBulletExplosion(){this._isExploding=!0,this.stopMovement(),this.createBulletExplosion(),x.instance.playSoundEffect("bulletHit")}handleDartHit(e,t){e?e.takeDamage(this.damage):t&&t.takeDamage(this.damage)}handleDartExplosion(){this._isExploding=!0,this.stopMovement(),this.createDartExplosion(),x.instance.playSoundEffect("DartHit")}handleRocketHit(e){this._isExploding=!0,this.stopMovement(),this.createRocketExplosion(),this.dealExplosionDamage(),this.clearPaintInRange(),x.instance.playSoundEffect("explosion")}handleTimeoutExplosion(){this._isExploding=!0,this.stopMovement(),this.createRocketExplosion(),this.clearPaintInRange(),this.dealExplosionDamage(),x.instance.playSoundEffect("explosion")}createBulletExplosion(){if(this.explosionPrefab){const e=p(this.explosionPrefab);this.node.addChild(e),e.setPosition(u.ZERO);const t=e.getComponent(y);t?(t.play("bulletexplosion"),this.scheduleOnce((()=>{e&&e.isValid&&e.destroy(),this.destroyBullet()}),.3)):g(e).to(.3,{scale:new u(1.5,1.5,1)}).delay(.3).call((()=>{e&&e.isValid&&e.destroy(),this.destroyBullet()})).start()}else this.destroyBullet()}createDartExplosion(){if(this.explosionPrefab){const e=p(this.explosionPrefab);this.node.addChild(e),e.setPosition(u.ZERO);const t=e.getComponent(y);t?(t.play("dartexplosion"),this.scheduleOnce((()=>{e&&e.isValid&&e.destroy(),this.destroyBullet()}),.3)):g(e).to(.3,{scale:new u(1.5,1.5,1)}).delay(.3).call((()=>{e&&e.isValid&&e.destroy(),this.destroyBullet()})).start()}else this.destroyBullet()}createRocketExplosion(){if(this.explosionPrefab){const e=p(this.explosionPrefab);this.node.addChild(e),e.setPosition(u.ZERO);const t=e.getComponent(y);t?(t.play("explosion"),this.scheduleOnce((()=>{e&&e.isValid&&e.destroy(),this.destroyBullet()}),.5)):g(e).to(.5,{scale:new u(2,2,1)}).delay(.5).call((()=>{e&&e.isValid&&e.destroy(),this.destroyBullet()})).start()}else this.destroyBullet()}dealExplosionDamage(){this.getAllVehiclesInRange().forEach((e=>{const t=r.distance(new r(this.node.worldPosition.x,this.node.worldPosition.y),new r(e.node.worldPosition.x,e.node.worldPosition.y));if(t<=this.explosionRadius){const i=1-t/this.explosionRadius,o=this.damage*i;e.takeDamage(o)}}))}clearPaintInRange(){const e=b.getInstance();if(!e)return void console.warn("Bullet: 无法获取GameManager单例");const t=e.getPaintManager();if(!t)return void console.warn("Bullet: 无法获取PaintManager实例");const i=new r(this.node.worldPosition.x,this.node.worldPosition.y),o=t.clearPaintInRange(i,this.explosionRadius);console.log(`火箭弹爆炸清除了 ${o} 个颜料`)}getAllVehiclesInRange(){var e,t;const i=[];((null==(e=this.node.scene)?void 0:e.getComponentsInChildren(E))||[]).forEach((e=>{this.getVehicleId(e.node)!==this._shooterId&&i.push(e)}));return((null==(t=this.node.scene)?void 0:t.getComponentsInChildren(R))||[]).forEach((e=>{this.getVehicleId(e.node)!==this._shooterId&&i.push(e)})),i}handleObstacleHit(){this.bulletType!==S.ROCKET?this.bulletType!==S.NORMAL?this.bulletType!==S.DART||this.handleDartExplosion():this.handleBulletExplosion():this.handleRocketHit(null)}stopMovement(){this._rigidBody&&(this._rigidBody.linearVelocity=r.ZERO)}getVehicleId(e){const t=e.getComponent(E),i=e.getComponent(R);return t?"player":i?i.getVehicleId?i.getVehicleId():"ai_unknown":"unknown"}playHitSound(){switch(this.bulletType){case S.NORMAL:x.instance.playSoundEffect("bulletHit");break;case S.DART:x.instance.playSoundEffect("dartHit");break;case S.ROCKET:x.instance.playSoundEffect("explosion")}}destroyBullet(){this.node&&this.node.isValid&&(this._pendingDestroy||(this._pendingDestroy=!0,f.once(m.EVENT_AFTER_PHYSICS,(()=>{this.node&&this.node.isValid&&this.node.destroy()}))))}}).prototype,"speed",[k],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 50}}),O=t(w.prototype,"damage",[k],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 5}}),A=t(w.prototype,"bulletType",[B],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return S.NORMAL}}),M=t(w.prototype,"lifeTime",[_],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 3}}),V=t(w.prototype,"explosionPrefab",[D],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),v=t(w.prototype,"explosionRadius",[C],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 300}}),P=w))||P));o._RF.pop()}}}));

System.register("chunks:///_virtual/camera_follow.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(t){var i,e,o,s,n,h,a,r,p,l;return{setters:[function(t){i=t.applyDecoratedDescriptor,e=t.initializerDefineProperty},function(t){o=t.cclegacy,s=t.Node,n=t._decorator,h=t.Component,a=t.Vec3,r=t.UITransform,p=t.Camera,l=t.view}],execute:function(){var d,c,m,u,_,g;o._RF.push({},"daf21OCa1hMm660rOlgyMop","camera_follow",void 0);const{ccclass:f,property:w}=n;t("CameraFollow",(d=f("CameraFollow"),c=w(s),d((_=i((u=class extends h{constructor(...t){super(...t),e(this,"target",_,this),e(this,"smooth",g,this),this._tempPos=new a,this._mapWidth=0,this._mapHeight=0}onLoad(){this._tempPos=new a,this._mapWidth=0,this._mapHeight=0}onDestroy(){this.target=null,this._tempPos=null}_detectMapSize(){if(!this.node||!this.node.parent)return;const t=this.node.parent.children.find((t=>"PlayGround"===t.name));if(t){const i=t.getComponentInChildren(r);i&&(this._mapWidth=i.contentSize.width*t.scale.x,this._mapHeight=i.contentSize.height*t.scale.y)}0!==this._mapWidth&&0!==this._mapHeight||(console.log("没有检测到地图尺寸"),this._mapWidth=2160,this._mapHeight=1440)}update(t){this.node&&this.node.isValid&&this._updateCameraPosition(t,!1)}_clampToMapBounds(t,i,e,o){if(!this.node||!this.node.parent)return[t,i];const s=this.node.parent.worldPosition.x,n=this.node.parent.worldPosition.y;let h=s-this._mapWidth/2+e/2,a=s+this._mapWidth/2-e/2,r=n-this._mapHeight/2+o/2,p=n+this._mapHeight/2-o/2,l=t,d=i;return l=h>a?s:t<h?h:t>a?a:t,d=r>p?n:i<r?r:i>p?p:i,[l,d]}_updateCameraPosition(t,i){if(!this.target||!this.node||!this.node.isValid)return;this.target.getWorldPosition(this._tempPos);const e=this.getComponent(p);if(!e)return;const o=l.getVisibleSize().width/l.getVisibleSize().height,s=e.orthoHeight,n=s*o,h=s,[a,r]=this._clampToMapBounds(this._tempPos.x,this._tempPos.y,n,h);let d=this.node.worldPosition;i?this.node.setWorldPosition(a,r,d.z):this.node.setWorldPosition(d.x+(a-d.x)*(1-Math.pow(1-this.smooth,60*t)),d.y+(r-d.y)*(1-Math.pow(1-this.smooth,60*t)),d.z)}init(t,i){if(this.target=i,t){const i=t.getComponent(r);i&&(this._mapWidth=i.contentSize.width*t.scale.x,this._mapHeight=i.contentSize.height*t.scale.y)}}}).prototype,"target",[c],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),g=i(u.prototype,"smooth",[w],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return.15}}),m=u))||m));o._RF.pop()}}}));

System.register("chunks:///_virtual/CarProperties.ts",["cc"],(function(r){var e,t;return{setters:[function(r){e=r.cclegacy,t=r._decorator}],execute:function(){var i,s;e._RF.push({},"796e3d2xZlPdZWRNarqrn+2","CarProperties",void 0);const{ccclass:a}=t;r("CarProperties",a("CarProperties")(((s=class{static getCarProperty(r){return this.carPropertiesConfig[r]||null}}).carPropertiesConfig={"car-1":{speed:25,steering:60,durability:40},"car-2":{speed:40,steering:40,durability:80},"car-3":{speed:60,steering:60,durability:60},"car-4":{speed:70,steering:70,durability:50},"car-5":{speed:65,steering:80,durability:60}},i=s))||i);e._RF.pop()}}}));

System.register("chunks:///_virtual/CarPropertyDisplay.ts",["./rollupPluginModLoBabelHelpers.js","cc","./CarProperties.ts"],(function(e){var r,t,i,s,o,a,n,p;return{setters:[function(e){r=e.applyDecoratedDescriptor,t=e.initializerDefineProperty},function(e){i=e.cclegacy,s=e.ProgressBar,o=e._decorator,a=e.Component,n=e.tween},function(e){p=e.CarProperties}],execute:function(){var l,u,g,h,d,c,y,P,B,b,m,f,C;i._RF.push({},"add91PL8SRFs5KSXc8GbptH","CarPropertyDisplay",void 0);const{ccclass:D,property:w}=o;e("CarPropertyDisplay",(l=D("CarPropertyDisplay"),u=w({type:s,tooltip:"速度进度条"}),g=w({type:s,tooltip:"转向进度条"}),h=w({type:s,tooltip:"坚硬度进度条"}),d=w({tooltip:"是否启用动画效果"}),c=w({tooltip:"动画持续时间（秒）"}),l((B=r((P=class extends a{constructor(...e){super(...e),t(this,"speedProgressBar",B,this),t(this,"steeringProgressBar",b,this),t(this,"durabilityProgressBar",m,this),t(this,"enableAnimation",f,this),t(this,"animationDuration",C,this)}onLoad(){this.autoFindProgressBars()}autoFindProgressBars(){if(!this.speedProgressBar){const e=this.node.getChildByName("speed")||this.node.getChildByName("Speed");e&&(this.speedProgressBar=e.getComponent(s))}if(!this.steeringProgressBar){const e=this.node.getChildByName("turn")||this.node.getChildByName("turn");e&&(this.steeringProgressBar=e.getComponent(s))}if(!this.durabilityProgressBar){const e=this.node.getChildByName("tough")||this.node.getChildByName("tough");e&&(this.durabilityProgressBar=e.getComponent(s))}}showCarProperties(e){const r=p.getCarProperty(e);r?(this.updatePropertyDisplay(r),this.node.active=!0):console.warn(`未找到车辆 ${e} 的属性配置`)}hideAllProperties(){this.node.active=!1}updatePropertyDisplay(e){this.speedProgressBar&&this.updateProgressBar(this.speedProgressBar,e.speed),this.steeringProgressBar&&this.updateProgressBar(this.steeringProgressBar,e.steering),this.durabilityProgressBar&&this.updateProgressBar(this.durabilityProgressBar,e.durability)}updateProgressBar(e,r){const t=r/100;this.enableAnimation?n(e).to(this.animationDuration,{progress:t}).start():e.progress=t}}).prototype,"speedProgressBar",[u],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),b=r(P.prototype,"steeringProgressBar",[g],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),m=r(P.prototype,"durabilityProgressBar",[h],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),f=r(P.prototype,"enableAnimation",[d],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!0}}),C=r(P.prototype,"animationDuration",[c],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return.5}}),y=P))||y));i._RF.pop()}}}));

System.register("chunks:///_virtual/GameHUD.ts",["./rollupPluginModLoBabelHelpers.js","cc","./GameManager.ts"],(function(e){var t,o,a,n,i,s,l,r,u,h;return{setters:[function(e){t=e.applyDecoratedDescriptor,o=e.initializerDefineProperty},function(e){a=e.cclegacy,n=e.Label,i=e.Button,s=e.ProgressBar,l=e._decorator,r=e.Component,u=e.Node},function(e){h=e.GameManager}],execute:function(){var g,c,p,m,d,b,y,D,f,B,w,R,U,H,G,L,M,T,C,P,A,v,E,z,I,_,N,O,S,$;a._RF.push({},"8d994pxBTRN65Jo2JjZ4MAH","GameHUD",void 0);const{ccclass:x,property:k}=l;e("GameHUD",(g=x("GameHUD"),c=k(n),p=k(n),m=k({type:n,tooltip:"AI车辆1的颜料占比显示标签"}),d=k({type:n,tooltip:"AI车辆2的颜料占比显示标签"}),b=k({type:n,tooltip:"AI车辆3的颜料占比显示标签"}),y=k({type:n,tooltip:"AI车辆4的颜料占比显示标签"}),D=k({type:i,tooltip:"射击按钮"}),f=k({type:n,tooltip:"弹药数量显示标签"}),B=k({type:s,tooltip:"弹药补充进度条"}),w=k({type:i,tooltip:"向上移动按钮"}),R=k({type:i,tooltip:"向下移动按钮"}),U=k({type:i,tooltip:"向左移动按钮"}),H=k({type:i,tooltip:"向右移动按钮"}),g((M=t((L=class extends r{constructor(...e){super(...e),o(this,"countdownLabel",M,this),o(this,"playerRatioLabel",T,this),o(this,"ai1RatioLabel",C,this),o(this,"ai2RatioLabel",P,this),o(this,"ai3RatioLabel",A,this),o(this,"ai4RatioLabel",v,this),o(this,"shootButton",E,this),o(this,"ammoLabel",z,this),o(this,"reloadProgressBar",I,this),o(this,"upButton",_,this),o(this,"downButton",N,this),o(this,"leftButton",O,this),o(this,"rightButton",S,this),o(this,"updateInterval",$,this),this.updateTimer=0,this.gameManager=null}onLoad(){this.updateTimer=0}start(){this.gameManager=h.getInstance(),this.gameManager?(this.initializeAIRatioDisplay(),this.initializeShootButton(),this.initializeTouchControlButtons()):console.error("GameHUD: GameManager未找到")}update(e){this.gameManager&&(this.updateTimer+=e,this.updateTimer>=this.updateInterval&&(this.updateCountdownDisplay(),this.updatePaintRatioDisplay(),this.updateAmmoDisplay(),this.updateTimer=0))}updateCountdownDisplay(){if(this.countdownLabel){const e=this.gameManager.getFormattedRemainingTime();this.countdownLabel.string=e;this.gameManager.getRemainingTime()<=30&&(this.countdownLabel.color=this.countdownLabel.color.lerp(new this.countdownLabel.color.constructor(255,0,0,255),.5))}}updatePaintRatioDisplay(){const e=this.gameManager.getAllVehiclePaintRatios(),t=e.player||0;this.updatePlayerRatioDisplay(t),this.updateAIRatiosDisplay(e)}updatePlayerRatioDisplay(e){const t=Math.round(100*e);this.playerRatioLabel&&(this.playerRatioLabel.string=`player: ${t}%`)}updateAIRatiosDisplay(e){const t=this.gameManager.getSortedVehiclePaintRatios().filter((e=>"player"!==e.vehicleId)),o=[this.ai1RatioLabel,this.ai2RatioLabel,this.ai3RatioLabel,this.ai4RatioLabel];t.forEach(((e,t)=>{if(t<o.length&&o[t]){const a=Math.round(100*e.ratio),n=this.getAIDisplayName(e.vehicleId);o[t].string=`${n}: ${a}%`}}));for(let e=t.length;e<o.length;e++)o[e]&&(o[e].string="")}getAIDisplayName(e){if(e.startsWith("ai_")){const t=e.split("_");if(t.length>=2)return`AI-${t[1]}`}return e}initializeAIRatioDisplay(){[this.ai1RatioLabel,this.ai2RatioLabel,this.ai3RatioLabel,this.ai4RatioLabel].forEach(((e,t)=>{e?e.string=`AI-${t+1}: 0%`:console.warn(`GameHUD: AI${t+1}RatioLabel未设置`)}))}resetHUD(){this.countdownLabel&&(this.countdownLabel.string="02:00",this.countdownLabel.color=new this.countdownLabel.color.constructor(255,255,255,255)),this.playerRatioLabel&&(this.playerRatioLabel.string="player: 0%"),this.initializeAIRatioDisplay()}initializeShootButton(){this.shootButton?this.shootButton.node.on(i.EventType.CLICK,this.onShootButtonClicked,this):console.warn("GameHUD: 射击按钮未设置")}onShootButtonClicked(){this.gameManager&&this.gameManager.playerShoot()}updateAmmoDisplay(){if(!this.gameManager)return;const e=this.gameManager.getPlayerComponent();if(e){if(this.ammoLabel){const t=e.getCurrentAmmo(),o=e.getMaxAmmo();this.ammoLabel.string=`${t}/${o}`}this.reloadProgressBar&&(e.isReloading()?(this.reloadProgressBar.node.active=!0,this.reloadProgressBar.progress=e.getReloadProgress()):this.reloadProgressBar.node.active=!1)}}initializeTouchControlButtons(){this.upButton?(this.upButton.node.on(u.EventType.TOUCH_START,this.onUpButtonPressed,this),this.upButton.node.on(u.EventType.TOUCH_END,this.onUpButtonReleased,this),this.upButton.node.on(u.EventType.TOUCH_CANCEL,this.onUpButtonReleased,this)):console.warn("GameHUD: 上移按钮未设置"),this.downButton?(this.downButton.node.on(u.EventType.TOUCH_START,this.onDownButtonPressed,this),this.downButton.node.on(u.EventType.TOUCH_END,this.onDownButtonReleased,this),this.downButton.node.on(u.EventType.TOUCH_CANCEL,this.onDownButtonReleased,this)):console.warn("GameHUD: 下移按钮未设置"),this.leftButton?(this.leftButton.node.on(u.EventType.TOUCH_START,this.onLeftButtonPressed,this),this.leftButton.node.on(u.EventType.TOUCH_END,this.onLeftButtonReleased,this),this.leftButton.node.on(u.EventType.TOUCH_CANCEL,this.onLeftButtonReleased,this)):console.warn("GameHUD: 左移按钮未设置"),this.rightButton?(this.rightButton.node.on(u.EventType.TOUCH_START,this.onRightButtonPressed,this),this.rightButton.node.on(u.EventType.TOUCH_END,this.onRightButtonReleased,this),this.rightButton.node.on(u.EventType.TOUCH_CANCEL,this.onRightButtonReleased,this)):console.warn("GameHUD: 右移按钮未设置")}onUpButtonPressed(){if(console.log("GameHUD: 上移按钮被按下"),this.gameManager){const e=this.gameManager.getPlayerComponent();e?(console.log("GameHUD: 设置玩家加速度为1"),e.setAcceleration(1)):console.warn("GameHUD: 无法获取玩家组件")}else console.warn("GameHUD: GameManager未找到")}onUpButtonReleased(){if(console.log("GameHUD: 上移按钮被释放"),this.gameManager){const e=this.gameManager.getPlayerComponent();e?(console.log("GameHUD: 设置玩家加速度为0"),e.setAcceleration(0)):console.warn("GameHUD: 无法获取玩家组件")}else console.warn("GameHUD: GameManager未找到")}onDownButtonPressed(){if(console.log("GameHUD: 下移按钮被按下"),this.gameManager){const e=this.gameManager.getPlayerComponent();e?(console.log("GameHUD: 设置玩家加速度为-1"),e.setAcceleration(-1)):console.warn("GameHUD: 无法获取玩家组件")}else console.warn("GameHUD: GameManager未找到")}onDownButtonReleased(){if(console.log("GameHUD: 下移按钮被释放"),this.gameManager){const e=this.gameManager.getPlayerComponent();e?(console.log("GameHUD: 设置玩家加速度为0"),e.setAcceleration(0)):console.warn("GameHUD: 无法获取玩家组件")}else console.warn("GameHUD: GameManager未找到")}onLeftButtonPressed(){if(console.log("GameHUD: 左移按钮被按下"),this.gameManager){const e=this.gameManager.getPlayerComponent();e?(console.log("GameHUD: 设置玩家转向为-1"),e.setDirection(-1)):console.warn("GameHUD: 无法获取玩家组件")}else console.warn("GameHUD: GameManager未找到")}onLeftButtonReleased(){if(console.log("GameHUD: 左移按钮被释放"),this.gameManager){const e=this.gameManager.getPlayerComponent();e?(console.log("GameHUD: 设置玩家转向为0"),e.setDirection(0)):console.warn("GameHUD: 无法获取玩家组件")}else console.warn("GameHUD: GameManager未找到")}onRightButtonPressed(){if(console.log("GameHUD: 右移按钮被按下"),this.gameManager){const e=this.gameManager.getPlayerComponent();e?(console.log("GameHUD: 设置玩家转向为1"),e.setDirection(1)):console.warn("GameHUD: 无法获取玩家组件")}else console.warn("GameHUD: GameManager未找到")}onRightButtonReleased(){if(console.log("GameHUD: 右移按钮被释放"),this.gameManager){const e=this.gameManager.getPlayerComponent();e?(console.log("GameHUD: 设置玩家转向为0"),e.setDirection(0)):console.warn("GameHUD: 无法获取玩家组件")}else console.warn("GameHUD: GameManager未找到")}}).prototype,"countdownLabel",[c],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),T=t(L.prototype,"playerRatioLabel",[p],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),C=t(L.prototype,"ai1RatioLabel",[m],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),P=t(L.prototype,"ai2RatioLabel",[d],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),A=t(L.prototype,"ai3RatioLabel",[b],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),v=t(L.prototype,"ai4RatioLabel",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),E=t(L.prototype,"shootButton",[D],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),z=t(L.prototype,"ammoLabel",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),I=t(L.prototype,"reloadProgressBar",[B],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),_=t(L.prototype,"upButton",[w],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),N=t(L.prototype,"downButton",[R],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),O=t(L.prototype,"leftButton",[U],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),S=t(L.prototype,"rightButton",[H],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),$=t(L.prototype,"updateInterval",[k],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return.1}}),G=L))||G));a._RF.pop()}}}));

System.register("chunks:///_virtual/GameManager.ts",["./rollupPluginModLoBabelHelpers.js","cc","./TempData.ts","./camera_follow.ts","./player.ts","./AIController.ts","./AIPlayer.ts","./PlayerManager.ts","./SceneTransition.ts","./SoundManager.ts","./PaintManager.ts","./GameOverPanel.ts","./GameHUD.ts","./Bullet.ts"],(function(e){var t,n,a,i,r,o,s,l,u,h,c,m,p,g,y,d,P,f,C,b,M,v,S,H,B,R,w,T;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.initializerDefineProperty},function(e){a=e.cclegacy,i=e.Node,r=e.ProgressBar,o=e.Label,s=e.Button,l=e._decorator,u=e.Component,h=e.resources,c=e.Prefab,m=e.instantiate,p=e.UITransform,g=e.director,y=e.Vec2},function(e){d=e.TempData},function(e){P=e.CameraFollow},function(e){f=e.player},function(e){C=e.AIController},function(e){b=e.AIPlayer},function(e){M=e.PlayerManager},function(e){v=e.SceneTransition},function(e){S=e.SoundManager},function(e){H=e.PaintManager},function(e){B=e.GameOverPanel},function(e){R=e.GameHUD},function(e){w=e.WeaponType,T=e.Bullet}],execute:function(){var G,N,E,I,A,D,L,U,x,z,O,$,_,F,V,W,k,j,K,J,q,Q,X,Y,Z,ee,te,ne;a._RF.push({},"b67f4UjjapGSoVG2Jvvuyl3","GameManager",void 0);const{ccclass:ae,property:ie}=l;let re=e("GameState",function(e){return e.RUNNING="running",e.PAUSED="paused",e.GAME_OVER="game_over",e}({}));e("GameManager",(G=ae("GameManager"),N=ie(i),E=ie(i),I=ie(i),A=ie(i),D=ie(r),L=ie(o),U=ie(s),x=ie(i),z=ie(i),O=ie(s),$=ie(s),_=ie(R),G(((ne=class e extends u{constructor(...e){super(...e),n(this,"playGround",W,this),n(this,"canvas",k,this),n(this,"spawnPoint",j,this),n(this,"camera",K,this),n(this,"playerHealthBar",J,this),n(this,"enemyCountLabel",q,this),n(this,"pauseButton",Q,this),n(this,"pausePanel",X,this),n(this,"gameOverPanel",Y,this),n(this,"resumeButton",Z,this),n(this,"mainMenuButton",ee,this),this.aiPlayers=[],this.currentState=re.RUNNING,this.gameStartTime=0,this.gameEndTime=0,this.gameDuration=90,this.remainingTime=90,this.playerHP=0,this.playerMaxHP=0,this.enemyCount=0,this.initialEnemyCount=0,this.playerComponent=null,this.paintManager=null,n(this,"gameHUD",te,this),this.bulletRoot=null}static getInstance(){return e._instance}onLoad(){if(e._instance)return console.log("销毁原有单例"),void this.node.destroy();e._instance=this}start(){console.log("调用场景内容加载"),this.initializeGame(),this.bindButtonEvents(),this.loadLevelAndCar()}update(e){this.currentState===re.RUNNING&&this.updateCountdown(e)}onDestroy(){e._instance===this&&(e._instance=null,console.log("GameManager 实例已销毁"))}initializeGame(){this.currentState=re.RUNNING,this.gameStartTime=Date.now(),this.gameEndTime=0,this.remainingTime=this.gameDuration,this.pausePanel&&(this.pausePanel.active=!1),this.gameOverPanel&&(this.gameOverPanel.active=!1),this.gameHUD&&this.gameHUD.resetHUD()}bindButtonEvents(){this.pauseButton&&this.pauseButton.node.on(s.EventType.CLICK,this.pauseGame,this),this.resumeButton&&this.resumeButton.node.on(s.EventType.CLICK,this.resumeGame,this),this.mainMenuButton&&this.mainMenuButton.node.on(s.EventType.CLICK,this.returnToLevelSelect,this)}loadLevelAndCar(){const e=d.selectedLevel,t=d.selectedCar;let n=null,a=null;e&&h.load(`prefab/levels/${e}`,c,((i,r)=>{!i&&r&&(n=m(r),n.setPosition(0,0,0),this.playGround.addChild(n),this.autoFindAIPlayers(),this.notifyAIControllers(),this.findBulletRoot(),this.initialEnemyCount=this.aiPlayers.length,this.refreshEnemyCount(this.initialEnemyCount),t&&h.load(`prefab/cars/${t}`,c,((e,i)=>{if(!e&&i){a=m(i);const e=this.spawnPoint.children;if(e.length>0){const t=e[Math.floor(Math.random()*e.length)],n=t.getWorldPosition(),i=this.canvas.getComponent(p).convertToNodeSpaceAR(n);a.setPosition(i),a.setRotation(t.getRotation());const r=a.getComponent(f);r&&(r.init(t.angle),this.initializePlayerHealth(r)),-1!==["point4","point5","point6"].indexOf(t.name)&&console.log("生成车辆在右侧")}this.canvas.addChild(a);const t=this.camera.getComponent(P);t&&n&&a&&t.init(n,a),this.initializePaintSystem()}e?console.error("加载车辆预制体失败:",e,t):i||console.error("未找到车辆预制体:",t)}))),i?console.error("加载关卡预制体失败:",i,e):r||console.error("未找到关卡预制体:",e)})),S.instance.playSoundEffect("carStart")}autoFindAIPlayers(){this.aiPlayers=[];const e=this.node.scene;if(!e)return;const t=e.getChildByName("Canvas");if(!t)return;const n=t.getChildByName("PlayGround");if(!n)return;const a=n.children[0];if(!a)return;const i=a.getChildByName("cars");if(i)for(const e of i.children){const t=e.getComponent(b);t&&this.aiPlayers.push(t)}}getAIPlayers(){return this.aiPlayers}findBulletRoot(){const e=this.node.scene;e?(this.bulletRoot=e.getChildByName("BulletRoot")||this.findNodeRecursively(e,"BulletRoot"),this.bulletRoot?console.log("BulletRoot节点找到:",this.bulletRoot.name):console.warn("BulletRoot节点未找到，子弹将添加到场景根节点")):console.warn("场景未找到")}findNodeRecursively(e,t){for(const n of e.children){if(n.name===t)return n;const e=this.findNodeRecursively(n,t);if(e)return e}return null}notifyAIControllers(){const e=this.node.scene.getComponentsInChildren(C);for(const t of e)t.onScenePrefabLoaded()}initializePlayerHealth(e){this.playerComponent=e,this.playerMaxHP=e.getMaxHealth(),this.playerHP=this.playerMaxHP,console.log(`玩家血量初始化完成: ${this.playerHP}/${this.playerMaxHP}`),this.refreshPlayerHealthBar()}reducePlayerHP(e){console.log("减少玩家血量:",e),this.playerHP=Math.max(0,this.playerHP-e),this.refreshPlayerHealthBar(),this.playerHP<=0&&this.currentState===re.RUNNING&&console.log("玩家血量归零，等待摧毁动画完成")}syncPlayerHealth(){this.playerComponent&&(this.playerHP=this.playerComponent.getCurrentHealth(),this.refreshPlayerHealthBar(),this.playerHP<=0&&this.currentState===re.RUNNING&&console.log("玩家血量归零，等待摧毁动画完成"))}resetPlayerHealth(){this.playerComponent&&(this.playerComponent.restoreVehicle(),this.playerHP=this.playerMaxHP,this.refreshPlayerHealthBar(),console.log(`玩家血量已重置: ${this.playerHP}/${this.playerMaxHP}`))}refreshPlayerHealthBar(){this.playerHealthBar&&this.playerMaxHP>0&&(this.playerHealthBar.progress=this.playerHP/this.playerMaxHP)}refreshEnemyCount(e){this.enemyCount=e,this.enemyCountLabel&&(this.enemyCountLabel.string=`opponent: ${this.enemyCount}`),this.enemyCount<=0&&this.currentState===re.RUNNING&&this.initialEnemyCount>0&&(console.log("所有AI车辆摧毁动画完成，触发游戏胜利"),this.gameOver(!0))}pauseGame(){this.currentState===re.RUNNING&&(this.currentState=re.PAUSED,g.pause(),this.pausePanel&&(this.pausePanel.active=!0),console.log("游戏已暂停"))}resumeGame(){this.currentState===re.PAUSED&&(this.currentState=re.RUNNING,g.resume(),this.pausePanel&&(this.pausePanel.active=!1),console.log("游戏已继续"))}gameOver(e){if(this.currentState===re.GAME_OVER)return;this.currentState=re.GAME_OVER,this.gameEndTime=Date.now(),g.pause();const t=this.calculateGameResult(e);if(this.gameOverPanel){this.gameOverPanel.active=!0;const n=this.gameOverPanel.getComponent(B);n&&(n.bindButtonEvents(),n.setGameOverInfo(e,t.performance,t.reward,t.gameTime,t.healthPercentage,t.stars))}M.instance.addMoney(t.reward),this.updateLevelProgress(t.stars,t.performance),M.instance.savePlayerData(),console.log(e?"游戏胜利！":"游戏失败！")}restartGame(){g.resume(),v.loadScene(g.getScene().name)}returnToLevelSelect(){g.resume(),v.loadScene("LevelSelect")}calculateGameResult(e){const t=(this.gameEndTime-this.gameStartTime)/1e3,n=this.playerHP/this.playerMaxHP;if(!e)return{performance:"",reward:10,gameTime:t,healthPercentage:n,stars:0};const a=this.calculateStars(t,n),{performance:i,reward:r}=this.calculatePerformance(t,n);return console.log(`游戏时长: ${t.toFixed(1)}秒, 生命值: ${(100*n).toFixed(1)}%, 星星: ${a}, 评价: ${i}, 奖励: ${r}金币`),{performance:i,reward:r,gameTime:t,healthPercentage:n,stars:a}}calculateStars(e,t){if(!this.paintManager)return 1;const n=this.paintManager.getPaintCountByOwner("player"),a=this.paintManager.getTotalPaintCount();if(0===a)return 1;const i=100*(n/a),r=this.enemyCount<=0&&this.initialEnemyCount>0;return i>=45||r?3:i>=35?2:i>=25?1:0}updateLevelProgress(e,t){const n=d.selectedLevel;n?(M.instance.updateLevelProgress(n,e,t),console.log(`关卡进度已更新: ${n}, 星星: ${e}`)):console.warn("无法获取当前关卡ID")}calculatePerformance(e,t){let n,a;switch(this.calculateStars(e,t)){case 3:n="S",a=300;break;case 2:n="A",a=200;break;case 1:n="B",a=100;break;default:n="F",a=20}return{performance:n,reward:a}}getCurrentState(){return this.currentState}getGameTime(){return this.gameEndTime>0?(this.gameEndTime-this.gameStartTime)/1e3:(Date.now()-this.gameStartTime)/1e3}getPlayerHP(){return this.playerHP}getPlayerMaxHP(){return this.playerMaxHP}getEnemyCount(){return this.enemyCount}initializePaintSystem(){this.paintManager=this.node.scene.getComponentInChildren(H)}clearAllPaint(){this.paintManager&&this.paintManager.clearAllPaint()}sprayPaint(e,t,n){this.paintManager&&e?this.paintManager.addPaint(e,t,n):console.warn("GameManager: 无法喷洒颜料，PaintManager或颜料预制体为空")}getAllVehiclePaintRatios(){return this.paintManager?this.paintManager.getAllPaintRatios():{}}getSortedVehiclePaintRatios(){if(this.paintManager){return this.paintManager.getSortedPaintRatios().map((e=>({vehicleId:e.ownerId,ratio:e.ratio,count:e.count})))}return[]}getPaintManager(){return this.paintManager}updateCountdown(e){this.remainingTime-=e,this.remainingTime<=0&&(this.remainingTime=0,this.onCountdownFinished())}onCountdownFinished(){console.log("倒计时结束，游戏结束"),this.gameOver(this.determineWinner())}determineWinner(){if(this.playerHP<=0)return console.log("玩家已死亡，游戏失败"),!1;if(this.enemyCount<=0&&this.initialEnemyCount>0)return console.log("所有AI车辆已被摧毁，游戏胜利"),!0;if(!this.paintManager)return!1;const e=this.paintManager.getPaintCountByOwner("player"),t=this.paintManager.getTotalPaintCount();if(0===t)return!1;const n=e/t;return console.log(`玩家颜料占比: ${(100*n).toFixed(1)}%`),n>.25?(console.log("玩家颜料占比超过25%，游戏胜利"),!0):(console.log("玩家颜料占比不足25%，游戏失败"),!1)}getRemainingTime(){return Math.max(0,this.remainingTime)}getFormattedRemainingTime(){const e=Math.ceil(this.getRemainingTime()),t=Math.floor(e/60),n=e%60;return`${t<10?"0"+t:t.toString()}:${n<10?"0"+n:n.toString()}`}playerShoot(){this.playerComponent&&this.playerComponent.shoot()}fireBullet(e,t,n,a,i){if(i===w.DART){const n=[new y(0,1),new y(1,0),new y(0,-1),new y(-1,0)];for(const r of n)this.createSingleBullet(e,t,r,a,i)}else this.createSingleBullet(e,t,n,a,i)}createSingleBullet(e,t,n,a,i){const r=m(e),o=r.getComponent(T);if(o&&(o.init(n,a),o.bulletType=i),this.bulletRoot){var s;const e=null==(s=this.bulletRoot.getComponent(p))?void 0:s.convertToNodeSpaceAR(t);e?r.setPosition(e):(console.warn("BulletRoot转换失败，请检查"),r.setWorldPosition(t)),this.bulletRoot.addChild(r),console.log("子弹已添加到BulletRoot节点")}}getPlayerComponent(){return this.playerComponent}})._instance=null,W=t((V=ne).prototype,"playGround",[N],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),k=t(V.prototype,"canvas",[E],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),j=t(V.prototype,"spawnPoint",[I],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),K=t(V.prototype,"camera",[A],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),J=t(V.prototype,"playerHealthBar",[D],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),q=t(V.prototype,"enemyCountLabel",[L],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Q=t(V.prototype,"pauseButton",[U],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),X=t(V.prototype,"pausePanel",[x],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Y=t(V.prototype,"gameOverPanel",[z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Z=t(V.prototype,"resumeButton",[O],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),ee=t(V.prototype,"mainMenuButton",[$],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),te=t(V.prototype,"gameHUD",[_],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),F=V))||F));a._RF.pop()}}}));

System.register("chunks:///_virtual/GameOverPanel.ts",["./rollupPluginModLoBabelHelpers.js","cc","./GameManager.ts"],(function(t){var e,i,r,a,n,l,o,s,u,c;return{setters:[function(t){e=t.applyDecoratedDescriptor,i=t.initializerDefineProperty},function(t){r=t.cclegacy,a=t.Label,n=t.Sprite,l=t.Button,o=t._decorator,s=t.Component,u=t.Color},function(t){c=t.GameManager}],execute:function(){var p,h,b,f,L,g,y,m,v,S,d,R,w,B,I,C,z,P,G,T,A,$,k,D,E,M,_,K,O,x,F;r._RF.push({},"85096jcoYBCMKDGuUlvynIB","GameOverPanel",void 0);const{ccclass:j,property:N}=o;t("GameOverPanel",(p=j("GameOverPanel"),h=N(a),b=N(a),f=N(a),L=N(a),g=N({type:n,tooltip:"第1颗星星精灵"}),y=N({type:n,tooltip:"第2颗星星精灵"}),m=N({type:n,tooltip:"第3颗星星精灵"}),v=N(a),S=N({type:a,tooltip:"AI车辆1的颜料占比显示标签"}),d=N({type:a,tooltip:"AI车辆2的颜料占比显示标签"}),R=N({type:a,tooltip:"AI车辆3的颜料占比显示标签"}),w=N({type:a,tooltip:"AI车辆4的颜料占比显示标签"}),B=N(l),I=N(l),p((P=e((z=class extends s{constructor(...t){super(...t),i(this,"titleLabel",P,this),i(this,"performanceLabel",G,this),i(this,"rewardLabel",T,this),i(this,"gameTimeLabel",A,this),i(this,"star1Sprite",$,this),i(this,"star2Sprite",k,this),i(this,"star3Sprite",D,this),i(this,"playerRatioLabel",E,this),i(this,"ai1RatioLabel",M,this),i(this,"ai2RatioLabel",_,this),i(this,"ai3RatioLabel",K,this),i(this,"ai4RatioLabel",O,this),i(this,"restartButton",x,this),i(this,"LevelSelectButton",F,this)}start(){}bindButtonEvents(){this.restartButton&&this.restartButton.node.on(l.EventType.CLICK,this.onRestartClick,this),this.LevelSelectButton&&this.LevelSelectButton.node.on(l.EventType.CLICK,this.onLevelSelectClick,this)}updatePaintRatios(){const t=c.getInstance();if(!t)return;const e=t.getAllVehiclePaintRatios().player||0,i=Math.round(100*e);this.playerRatioLabel&&(this.playerRatioLabel.string=`player: ${i}%`);const r=t.getSortedVehiclePaintRatios().filter((t=>"player"!==t.vehicleId)),a=[this.ai1RatioLabel,this.ai2RatioLabel,this.ai3RatioLabel,this.ai4RatioLabel];r.forEach(((t,e)=>{if(e<a.length&&a[e]){const i=Math.round(100*t.ratio),r=this.getAIDisplayName(t.vehicleId);a[e].string=`${r}: ${i}%`}}));for(let t=r.length;t<a.length;t++)a[t]&&(a[t].string="")}getAIDisplayName(t){if(t.startsWith("ai_")){const e=t.split("_");if(e.length>=2)return`AI-${e[1]}`}return t}updateStarSprites(t){[this.star1Sprite,this.star2Sprite,this.star3Sprite].forEach(((e,i)=>{e&&(e.color=new u(255,255,255,i<t?255:100))})),console.log(`更新星星显示: ${t}/3 颗星星亮起`)}onRestartClick(){const t=c.getInstance();t&&t.restartGame()}onLevelSelectClick(){const t=c.getInstance();t&&t.returnToLevelSelect()}setGameOverInfo(t,e,i,r,a,n){this.titleLabel&&(this.titleLabel.string=t?"winner":"loser"),this.performanceLabel&&(this.performanceLabel.string=`performance: ${e}`),this.rewardLabel&&(this.rewardLabel.string=`reward: ${i}`),this.gameTimeLabel&&(this.gameTimeLabel.string=`time: ${r.toFixed(1)}S`),this.updateStarSprites(n),this.updatePaintRatios()}onDestroy(){this.restartButton&&this.restartButton.node&&this.restartButton.node.off(l.EventType.CLICK,this.onRestartClick,this),this.LevelSelectButton&&this.LevelSelectButton.node&&this.LevelSelectButton.node.off(l.EventType.CLICK,this.onLevelSelectClick,this)}}).prototype,"titleLabel",[h],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),G=e(z.prototype,"performanceLabel",[b],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),T=e(z.prototype,"rewardLabel",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),A=e(z.prototype,"gameTimeLabel",[L],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),$=e(z.prototype,"star1Sprite",[g],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),k=e(z.prototype,"star2Sprite",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),D=e(z.prototype,"star3Sprite",[m],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),E=e(z.prototype,"playerRatioLabel",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),M=e(z.prototype,"ai1RatioLabel",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),_=e(z.prototype,"ai2RatioLabel",[d],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),K=e(z.prototype,"ai3RatioLabel",[R],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),O=e(z.prototype,"ai4RatioLabel",[w],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),x=e(z.prototype,"restartButton",[B],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),F=e(z.prototype,"LevelSelectButton",[I],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),C=z))||C));r._RF.pop()}}}));

System.register("chunks:///_virtual/HealthBarUI.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(t){var e,s,i,o,a;return{setters:[function(t){e=t.applyDecoratedDescriptor,s=t.initializerDefineProperty},function(t){i=t.cclegacy,o=t._decorator,a=t.Component}],execute:function(){var n,r,h;i._RF.push({},"79d6fgSlVRCYoTh/XQ+YRv5","HealthBarUI",void 0);const{ccclass:l,property:c}=o;t("HealthBarUI",l("HealthBarUI")((h=e((r=class extends a{constructor(...t){super(...t),s(this,"offsetY",h,this),this.targetNode=null,this.canvas=null,this.isInitialized=!1}start(){this.setupCanvas(),this.targetNode=this.node.parent,this.separateFromParent(),console.log("血条UI初始化完成")}setupCanvas(){this.canvas=this.node.scene.getChildByName("Canvas"),this.canvas||console.error("未找到Canvas节点，血条可能无法正确显示")}separateFromParent(){if(!this.canvas||!this.targetNode)return;const t=this.node.worldPosition;this.node.setParent(this.canvas),this.node.setWorldPosition(t),this.isInitialized=!0,console.log("血条已分离到Canvas下")}update(){this.isInitialized&&this.targetNode&&this.canvas&&this.updatePosition()}updatePosition(){const t=this.targetNode.worldPosition;this.node.setWorldPosition(t.x,t.y+this.offsetY,t.z),this.node.setRotationFromEuler(0,0,0)}setOffsetY(t){this.offsetY=t}setVisible(t){this.node.active=t}getOffsetY(){return this.offsetY}setTarget(t){this.targetNode=t}destroyHealthBar(){this.node.destroy()}}).prototype,"offsetY",[c],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 50}}),n=r))||n);i._RF.pop()}}}));

System.register("chunks:///_virtual/main",["./AIController.ts","./AIPlayer.ts","./Bullet.ts","./CarProperties.ts","./CarPropertyDisplay.ts","./GameHUD.ts","./GameManager.ts","./GameOverPanel.ts","./HealthBarUI.ts","./MainMenuController.ts","./PaintManager.ts","./PaintSpot.ts","./PausePanel.ts","./PlayerInfoUI.ts","./PlayerManager.ts","./PurchasePanel.ts","./SceneTransition.ts","./SelectManager.ts","./SoundManager.ts","./TempData.ts","./camera_follow.ts","./player.ts"],(function(){return{setters:[null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null],execute:function(){}}}));

System.register("chunks:///_virtual/MainMenuController.ts",["./rollupPluginModLoBabelHelpers.js","cc","./SceneTransition.ts","./SoundManager.ts","./PlayerManager.ts"],(function(e){var t,n,i,s,o,l,r,a,u,c,h;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.initializerDefineProperty},function(e){i=e.cclegacy,s=e.Button,o=e.Node,l=e.Label,r=e._decorator,a=e.Component},function(e){u=e.SceneTransition},function(e){c=e.SoundManager},function(e){h=e.PlayerManager}],execute:function(){var p,f,b,g,d,P,B,C,y,m,v,L,R,S,w,z,M,E,T,K,I,k,A,G,D,H,_,x,F;i._RF.push({},"0cf64BckYpA8bODaKn8c5t/","MainMenuController",void 0);const{ccclass:j,property:N}=r;e("MainMenuController",(p=j("MainMenuController"),f=N(s),b=N(s),g=N(s),d=N(s),P=N(o),B=N(l),C=N(s),y=N(s),m=N(o),v=N(s),L=N(o),R=N(s),S=N(s),p((M=t((z=class extends a{constructor(...e){super(...e),n(this,"startGameBtn",M,this),n(this,"settingBtn",E,this),n(this,"closesettingBtn",T,this),n(this,"audioBtn",K,this),n(this,"settingPanel",I,this),n(this,"audioLabel",k,this),n(this,"helpButton",A,this),n(this,"closehelpBtn",G,this),n(this,"helpPanel",D,this),n(this,"resetProgressBtn",H,this),n(this,"resetProgressConfirmPanel",_,this),n(this,"confirmResetBtn",x,this),n(this,"closeResetPanelBtn",F,this)}start(){this.startGameBtn&&this.startGameBtn.node.on(s.EventType.CLICK,this.onStartGame,this),this.settingBtn&&this.settingBtn.node.on(s.EventType.CLICK,this.displaySettingPanel,this),this.closesettingBtn&&this.closesettingBtn.node.on(s.EventType.CLICK,this.hideSettingPanel,this),this.helpButton&&this.helpButton.node.on(s.EventType.CLICK,this.displayHelpPanel,this),this.closehelpBtn&&this.closehelpBtn.node.on(s.EventType.CLICK,this.hideHelpPanel,this),this.audioBtn&&this.audioBtn.node.on(s.EventType.CLICK,this.onAudioClick,this),this.resetProgressBtn&&this.resetProgressBtn.node.on(s.EventType.CLICK,this.onResetProgress,this),this.confirmResetBtn&&this.confirmResetBtn.node.on(s.EventType.CLICK,this.onConfirmReset,this),this.closeResetPanelBtn&&this.closeResetPanelBtn.node.on(s.EventType.CLICK,this.closeResetPanel,this),this.updateAudioButtonLabel()}displaySettingPanel(){this.settingPanel.active=!0}hideSettingPanel(){this.settingPanel.active=!1}displayHelpPanel(){this.helpPanel.active=!0}hideHelpPanel(){this.helpPanel.active=!1}onAudioClick(){c.instance.toggleAudio(),this.updateAudioButtonLabel()}updateAudioButtonLabel(){this.audioLabel&&(this.audioLabel.string=c.instance.isMuted()?"音效:关 \n sound:off":"音效:开\n sound:on")}onStartGame(){c.instance.playSoundEffect("buttonClick"),u.loadScene("LevelSelect")}onResetProgress(){c.instance.playSoundEffect("buttonClick"),this.showResetConfirmPanel()}showResetConfirmPanel(){this.resetProgressConfirmPanel&&(this.resetProgressConfirmPanel.active=!0)}closeResetPanel(){this.resetProgressConfirmPanel&&(this.resetProgressConfirmPanel.active=!1)}onConfirmReset(){c.instance.playSoundEffect("buttonClick"),this.closeResetPanel(),h.instance&&(h.instance.resetPlayerData(),console.log("玩家进度已重置"))}}).prototype,"startGameBtn",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),E=t(z.prototype,"settingBtn",[b],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),T=t(z.prototype,"closesettingBtn",[g],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),K=t(z.prototype,"audioBtn",[d],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),I=t(z.prototype,"settingPanel",[P],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),k=t(z.prototype,"audioLabel",[B],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),A=t(z.prototype,"helpButton",[C],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),G=t(z.prototype,"closehelpBtn",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),D=t(z.prototype,"helpPanel",[m],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),H=t(z.prototype,"resetProgressBtn",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),_=t(z.prototype,"resetProgressConfirmPanel",[L],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),x=t(z.prototype,"confirmResetBtn",[R],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),F=t(z.prototype,"closeResetPanelBtn",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),w=z))||w));i._RF.pop()}}}));

System.register("chunks:///_virtual/PaintManager.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(t){var n,e,i,a,o,r,s,c,p,h,d;return{setters:[function(t){n=t.applyDecoratedDescriptor,e=t.initializerDefineProperty},function(t){i=t.cclegacy,a=t._decorator,o=t.Component,r=t.Node,s=t.Layers,c=t.UITransform,p=t.Vec2,h=t.instantiate,d=t.Vec3}],execute:function(){var l,u,g,P;i._RF.push({},"7dd7aydfZBHRbtWPJmh00Xf","PaintManager",void 0);const{ccclass:f,property:C}=a;t("PaintManager",f("PaintManager")(((P=class t extends o{constructor(...t){super(...t),this.paintMap=new Map,e(this,"coverageRadius",g,this),this.paintContainer=null}static getInstance(){return t._instance}onLoad(){if(t._instance)return console.log("销毁原有PaintManager单例"),void this.node.destroy();t._instance=this,this.paintContainer=new r("PaintContainer"),this.paintContainer.layer=s.Enum.UI_2D,this.node.addChild(this.paintContainer),console.log("PaintManager初始化完成")}onDestroy(){t._instance===this&&(t._instance=null,console.log("PaintManager实例已销毁"))}addPaint(t,n,e){if(!t||!this.paintContainer)return void console.warn("PaintManager: 颜料预制体或容器为空");if(!this.paintContainer.getComponent(c)){this.paintContainer.addComponent(c).setContentSize(1280,720)}const i=this.paintContainer.getComponent(c).convertToNodeSpaceAR(n),a=new p(i.x,i.y);if(this.isNearOwnPaint(a,e))return;this.checkAndRemoveOverlappingPaint(a,e);const o=h(t);o.setPosition(i),o.layer=s.Enum.UI_2D,this.paintContainer.addChild(o);const r=this.generatePaintId(a,e),d={node:o,position:a,ownerId:e,timestamp:Date.now()};this.paintMap.set(r,d)}checkAndRemoveOverlappingPaint(t,n){const e=[];this.paintMap.forEach(((i,a)=>{p.distance(i.position,t)<this.coverageRadius&&i.ownerId!==n&&e.push(a)})),e.forEach((t=>{this.removePaint(t)}))}removePaint(t){const n=this.paintMap.get(t);n&&(n.node&&n.node.isValid&&n.node.destroy(),this.paintMap.delete(t))}removePaintById(t){this.removePaint(t)}isNearOwnPaint(t,n){for(const e of this.paintMap.values())if(e.ownerId===n){if(p.distance(e.position,t)<this.coverageRadius)return!0}return!1}generatePaintId(t,n){const e=Date.now();return`paint_${n}_${t.x.toFixed(0)}_${t.y.toFixed(0)}_${e}`}clearAllPaint(){this.paintMap.forEach((t=>{t.node&&t.node.isValid&&t.node.destroy()})),this.paintMap.clear(),console.log("清除了所有颜料")}getPaintCountByOwner(t){let n=0;return this.paintMap.forEach((e=>{e.ownerId===t&&n++})),n}getTotalPaintCount(){return this.paintMap.size}getPaintRatioByOwner(t){const n=this.getTotalPaintCount();if(0===n)return 0;return this.getPaintCountByOwner(t)/n}getAllPaintRatios(){const t={},n=this.getTotalPaintCount();if(0===n)return t;const e={};this.paintMap.forEach((t=>{const n=t.ownerId;e[n]=(e[n]||0)+1}));for(const i in e)t[i]=e[i]/n;return t}getSortedPaintRatios(){const t=this.getAllPaintRatios(),n=[];for(const e in t)n.push({ownerId:e,ratio:t[e],count:this.getPaintCountByOwner(e)});return n.sort(((t,n)=>n.ratio-t.ratio)),n}clearPaintInRange(t,n){if(!this.paintContainer)return console.warn("PaintManager: 颜料容器未初始化"),0;const e=this.paintContainer.getComponent(c);if(!e)return console.warn("PaintManager: 颜料容器缺少UITransform组件"),0;const i=new d(t.x,t.y,0),a=e.convertToNodeSpaceAR(i),o=new p(a.x,a.y),r=[];return this.paintMap.forEach(((t,e)=>{p.distance(t.position,o)<=n&&r.push(e)})),r.forEach((t=>{this.removePaint(t)})),console.log(`清除了 ${r.length} 个范围内的颜料`),r.length}})._instance=null,g=n((u=P).prototype,"coverageRadius",[C],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 30}}),l=u))||l);i._RF.pop()}}}));

System.register("chunks:///_virtual/PaintSpot.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(t){var e,i,n,o,a,r;return{setters:[function(t){e=t.applyDecoratedDescriptor,i=t.initializerDefineProperty},function(t){n=t.cclegacy,o=t._decorator,a=t.Component,r=t.Sprite}],execute:function(){var s,l,p,c;n._RF.push({},"e9c7fJf5G9M8ZAl2uR3FDyp","PaintSpot",void 0);const{ccclass:h,property:u}=o;t("PaintSpot",h("PaintSpot")((p=e((l=class extends a{constructor(...t){super(...t),i(this,"fadeTime",p,this),i(this,"enableFade",c,this),this.sprite=null,this.originalAlpha=1,this.creationTime=0}onLoad(){this.sprite=this.getComponent(r),this.sprite&&(this.originalAlpha=this.sprite.color.a/255),this.creationTime=Date.now()}start(){this.playSpawnAnimation()}update(t){}playSpawnAnimation(){this.node&&(this.node.setScale(.1,.1,1),this.scheduleOnce((()=>{this.node&&this.node.isValid&&this.node.setScale(1,1,1)}),.1))}}).prototype,"fadeTime",[u],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 30}}),c=e(l.prototype,"enableFade",[u],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!1}}),s=l))||s);n._RF.pop()}}}));

System.register("chunks:///_virtual/PausePanel.ts",["./rollupPluginModLoBabelHelpers.js","cc","./GameManager.ts"],(function(t){var e,n,i,s,o,u,r;return{setters:[function(t){e=t.applyDecoratedDescriptor,n=t.initializerDefineProperty},function(t){i=t.cclegacy,s=t.Button,o=t._decorator,u=t.Component},function(t){r=t.GameManager}],execute:function(){var a,c,l,h,p,m,C,B,f;i._RF.push({},"5a320cJxNpIpp4K8fCIQRf7","PausePanel",void 0);const{ccclass:M,property:y}=o;t("PausePanel",(a=M("PausePanel"),c=y(s),l=y(s),h=y(s),a((C=e((m=class extends u{constructor(...t){super(...t),n(this,"resumeButton",C,this),n(this,"restartButton",B,this),n(this,"mainMenuButton",f,this)}start(){this.bindButtonEvents()}bindButtonEvents(){this.resumeButton&&this.resumeButton.node.on(s.EventType.CLICK,this.onResumeClick,this),this.restartButton&&this.restartButton.node.on(s.EventType.CLICK,this.onRestartClick,this),this.mainMenuButton&&this.mainMenuButton.node.on(s.EventType.CLICK,this.onMainMenuClick,this)}onResumeClick(){const t=r.getInstance();t&&t.resumeGame()}onRestartClick(){const t=r.getInstance();t&&t.restartGame()}onMainMenuClick(){const t=r.getInstance();t&&t.returnToMainMenu()}onDestroy(){this.resumeButton&&this.resumeButton.node.off(s.EventType.CLICK,this.onResumeClick,this),this.restartButton&&this.restartButton.node.off(s.EventType.CLICK,this.onRestartClick,this),this.mainMenuButton&&this.mainMenuButton.node.off(s.EventType.CLICK,this.onMainMenuClick,this)}}).prototype,"resumeButton",[c],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),B=e(m.prototype,"restartButton",[l],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),f=e(m.prototype,"mainMenuButton",[h],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),p=m))||p));i._RF.pop()}}}));

System.register("chunks:///_virtual/player.ts",["./rollupPluginModLoBabelHelpers.js","cc","./AIPlayer.ts","./GameManager.ts","./SoundManager.ts","./Bullet.ts"],(function(t){var e,i,n,o,r,s,a,l,h,c,d,p,y,u,_,g,m,f,b,D,w,A,R;return{setters:[function(t){e=t.applyDecoratedDescriptor,i=t.initializerDefineProperty},function(t){n=t.cclegacy,o=t.SpriteFrame,r=t.Prefab,s=t._decorator,a=t.Component,l=t.Vec2,h=t.input,c=t.Input,d=t.BoxCollider2D,p=t.Contact2DType,y=t.RigidBody2D,u=t.ERigidBody2DType,_=t.Sprite,g=t.KeyCode,m=t.tween,f=t.Vec3},function(t){b=t.AIPlayer},function(t){D=t.GameManager},function(t){w=t.SoundManager},function(t){A=t.WeaponType,R=t.Bullet}],execute:function(){var P,B,T,S,E,V,v,x,C,z,I,M,F,k,H,K,O,W,N,U,Y,G,L,$,J,j,q,Q,Z,X;n._RF.push({},"b39243E9/5JHJc2plQZwxL1","player",void 0);const{ccclass:tt,property:et}=s;t("player",(P=tt("player"),B=et(o),T=et(r),S=et({type:r,tooltip:"普通子弹预制体"}),E=et({type:r,tooltip:"火焰预制体"}),V=et({type:r,tooltip:"火箭弹预制体"}),v=et({tooltip:"射速（发/秒）"}),x=et({type:A,tooltip:"武器类型"}),C=et({tooltip:"最大弹药数量"}),z=et({tooltip:"弹药补充时间（秒）"}),P((F=e((M=class extends a{constructor(...t){super(...t),i(this,"maxSpeed",F,this),i(this,"acceleration",k,this),i(this,"brakeDeceleration",H,this),i(this,"turnSpeed",K,this),i(this,"friction",O,this),i(this,"initAngle",W,this),i(this,"maxHealth",N,this),i(this,"destroyedSprite",U,this),i(this,"removeDelay",Y,this),i(this,"paintPrefab",G,this),i(this,"paintSprayInterval",L,this),i(this,"normalBulletPrefab",$,this),i(this,"dartPrefab",J,this),i(this,"rocketPrefab",j,this),i(this,"fireRate",q,this),i(this,"weaponType",Q,this),i(this,"maxAmmo",Z,this),i(this,"ammoReloadTime",X,this),this._rigidBody=null,this._direction=0,this._accel=0,this._angle=-90,this._targetAngle=-90,this._lastValidPosition=new l,this._currentHealth=100,this._isDestroyed=!1,this._originalSprite=null,this._paintTimer=0,this._vehicleId="player",this._canFire=!0,this._fireTimer=0,this._currentAmmo=20,this._isReloading=!1,this._reloadTimer=0}onLoad(){this._rigidBody=null,this._direction=0,this._accel=0,this._angle=-90,this._targetAngle=-90,this._lastValidPosition=new l,this._currentHealth=this.maxHealth,this._isDestroyed=!1,this._paintTimer=0,this._vehicleId="player",this._canFire=!0,this._fireTimer=0,this._currentAmmo=this.maxAmmo,this._isReloading=!1,this._reloadTimer=0}onEnable(){h.on(c.EventType.KEY_DOWN,this.onKeyDown,this),h.on(c.EventType.KEY_UP,this.onKeyUp,this)}onDisable(){h.off(c.EventType.KEY_DOWN,this.onKeyDown,this),h.off(c.EventType.KEY_UP,this.onKeyUp,this)}onDestroy(){h.off(c.EventType.KEY_DOWN,this.onKeyDown,this),h.off(c.EventType.KEY_UP,this.onKeyUp,this),this._rigidBody=null;const t=this.getComponent(d);t&&t.off(p.BEGIN_CONTACT,this.onBeginContact,this)}start(){if(this._rigidBody=this.getComponent(y),!this._rigidBody||!this.node||!this.node.isValid)return void console.error("player requires RigidBody2D component and valid node");this._rigidBody.type=u.Dynamic,this._rigidBody.allowSleep=!1,this._rigidBody.gravityScale=0,this._rigidBody.linearDamping=.3,this._rigidBody.angularDamping=.9,this._rigidBody.fixedRotation=!0,this._lastValidPosition=new l(this.node.worldPosition.x,this.node.worldPosition.y),this._angle=this.initAngle,this._targetAngle=this.initAngle,this.node.setRotationFromEuler(0,0,this.initAngle);const t=this.getComponent(_);t&&t.spriteFrame&&(this._originalSprite=t.spriteFrame);const e=this.getComponent(d);e?(console.log("BoxCollider2D component found and registered"),e.on(p.BEGIN_CONTACT,this.onBeginContact,this)):console.error("BoxCollider2D component not found")}onKeyDown(t){switch(t.keyCode){case g.ARROW_UP:this._accel=1;break;case g.ARROW_DOWN:this._accel=-1;break;case g.ARROW_LEFT:w.instance.playSoundEffect("carDrift"),this._direction=-1;break;case g.ARROW_RIGHT:w.instance.playSoundEffect("carDrift"),this._direction=1;break;case g.SPACE:this.shoot()}}onKeyUp(t){switch(t.keyCode){case g.ARROW_UP:1===this._accel&&(this._accel=0);break;case g.ARROW_DOWN:-1===this._accel&&(this._accel=0);break;case g.ARROW_LEFT:-1===this._direction&&(this._direction=0);break;case g.ARROW_RIGHT:1===this._direction&&(this._direction=0)}}update(t){if(!this._rigidBody||!this.node||!this.node.isValid)return;if(this._isDestroyed)return;const e=this._rigidBody.linearVelocity,i=e.length(),n=new l(this.node.worldPosition.x,this.node.worldPosition.y);if(0!==this._direction){const e=this.turnSpeed*t*this._direction;this._targetAngle-=e}const o=this._targetAngle-this._angle;if(Math.abs(o)>.1?this._angle+=.1*o:this._angle=this._targetAngle,this.node.setRotationFromEuler(0,0,this._angle),1===this._accel){const t=(this._angle+90)*Math.PI/180,e=new l(Math.cos(t)*this.acceleration,Math.sin(t)*this.acceleration);this._rigidBody.applyForce(e,n,!0)}else if(-1===this._accel){const t=(this._angle+90)*Math.PI/180,i=new l(Math.cos(t),Math.sin(t));if(e.dot(i)>0){const t=i.clone().multiplyScalar(-this.brakeDeceleration);this._rigidBody.applyForce(t,n,!0)}else{const t=i.clone().multiplyScalar(.5*-this.acceleration);this._rigidBody.applyForce(t,n,!0)}}else if(i>1){const t=e.clone().multiplyScalar(2*-this.friction);this._rigidBody.applyForce(t,n,!0)}if(i>this.maxSpeed){const t=e.clone().normalize();this._rigidBody.linearVelocity=t.multiplyScalar(this.maxSpeed)}if(i<.1){l.distance(n,this._lastValidPosition)>50&&(this.node.setWorldPosition(this._lastValidPosition.x,this._lastValidPosition.y,this.node.worldPosition.z),this._rigidBody.linearVelocity=new l(0,0))}else this._lastValidPosition=n.clone();Math.abs(this._angle)>360&&(this._angle=this._angle%360,this._targetAngle=this._targetAngle%360),this.updatePaintSpray(t),this.updateWeaponSystem(t)}init(t){this.initAngle=t,this._angle=t,this._targetAngle=t,this.node.setRotationFromEuler(0,0,t)}getRigidBody(){return this._rigidBody}onBeginContact(t,e,i){w.instance.playSoundEffect("carCollision"),console.log("玩家车辆发生碰撞，碰撞对象:",e.node.name);const n=e.node,o=n.getComponent(b);if(o){console.log("碰撞对象是AI车辆:",n.name);const t=this._rigidBody.linearVelocity.length(),e=o.node.getComponent(y),i=e?e.linearVelocity.length():0,r=.5,s=Math.round(t*r),a=Math.round(i*r);o.takeDamage(s);const h=new l(this._rigidBody.linearVelocity.x,this._rigidBody.linearVelocity.y);h.normalize(),h.multiplyScalar(.05*-t),this._rigidBody.linearVelocity=h,this.takeDamage(a)}else{const t=n.getComponent(R);if(t){if("player"===t._shooterId)return void console.log("玩家与自己发射的子弹碰撞，不造成伤害和反作用力");{const e=t.damage||5;if(this.takeDamage(e),0===t.bulletType){const t=new l(this._rigidBody.linearVelocity.x,this._rigidBody.linearVelocity.y);t.normalize(),t.multiplyScalar(.5*-e),this._rigidBody.linearVelocity=t}console.log("玩家被子弹击中，造成伤害:",e)}}else{const t=this._rigidBody.linearVelocity.length(),e=.3,i=Math.round(t*e),n=new l(this._rigidBody.linearVelocity.x,this._rigidBody.linearVelocity.y);n.normalize(),n.multiplyScalar(.05*-t),this._rigidBody.linearVelocity=n,this.takeDamage(i)}}}takeDamage(t){if(this._isDestroyed)return;this._currentHealth=Math.max(0,this._currentHealth-t),console.log(`玩家受到伤害: ${t}, 剩余生命值: ${this._currentHealth}`);const e=D.getInstance();e&&e.syncPlayerHealth(),this._currentHealth<=0&&this.destroyVehicle()}destroyVehicle(){if(!this._isDestroyed){if(w.instance.playSoundEffect("carDestruction"),this._isDestroyed=!0,console.log("玩家车辆被摧毁！"),this.destroyedSprite){const t=this.getComponent(_);t&&(t.spriteFrame=this.destroyedSprite)}this.disableInput(),this.startDestroyAnimation()}}disableInput(){h.off(c.EventType.KEY_DOWN,this.onKeyDown,this),h.off(c.EventType.KEY_UP,this.onKeyUp,this),this._direction=0,this._accel=0}removeVehicleNode(){this.node&&this.node.isValid&&(console.log("移除玩家车辆节点"),this.node.removeFromParent())}startDestroyAnimation(){this.node&&m(this.node).to(2,{scale:new f(1,1,1),angle:this.node.angle+180}).call((()=>{this.onDestroyAnimationComplete()})).start()}onDestroyAnimationComplete(){console.log("玩家车辆摧毁动画完成，触发游戏结束");const t=D.getInstance();t&&t.gameOver(!1)}getCurrentHealth(){return this._currentHealth}getMaxHealth(){return this.maxHealth}isDestroyed(){return this._isDestroyed}restoreVehicle(){if(this.unschedule(this.removeVehicleNode),this._isDestroyed=!1,this._currentHealth=this.maxHealth,this._originalSprite){const t=this.getComponent(_);t&&(t.spriteFrame=this._originalSprite)}this.onEnable(),this.node&&(this.node.setScale(1,1),this.node.angle=this.initAngle),this._rigidBody&&(this._rigidBody.linearVelocity=new l(0,0)),console.log("玩家车辆已恢复")}updatePaintSpray(t){!this._isDestroyed&&this.paintPrefab&&(this._paintTimer+=t,this._paintTimer>=this.paintSprayInterval&&(this.sprayPaint(),this._paintTimer=0))}sprayPaint(){const t=D.getInstance();if(!t)return void console.warn("GameManager未找到，无法喷洒颜料");const e=this.node.getWorldPosition();t.sprayPaint(this.paintPrefab,e,this._vehicleId)}updateWeaponSystem(t){if(this._isDestroyed)return;this._fireTimer+=t;const e=1/this.fireRate;this._fireTimer>=e&&(this._canFire=!0),this.updateAmmoReload(t)}updateAmmoReload(t){this._isReloading&&(this._reloadTimer+=t,this._reloadTimer>=this.ammoReloadTime&&(this._currentAmmo=this.maxAmmo,this._isReloading=!1,this._reloadTimer=0,console.log("弹药补充完成！")))}shoot(){if(!this._canFire||this._isDestroyed)return;if(this._currentAmmo<=0)return void(this._isReloading||this.startReload());this._canFire=!1,this._fireTimer=0,this._currentAmmo--;let t=null;switch(this.weaponType){case A.NORMAL:console.log("发射普通子弹"),t=this.normalBulletPrefab;break;case A.DART:t=this.dartPrefab;break;case A.ROCKET:t=this.rocketPrefab}if(!t)return console.warn("子弹预制体未设置"),void(this._canFire=!0);const e=(this._angle+90)*Math.PI/180,i=new l(Math.cos(e),Math.sin(e)),n=this.node.worldPosition,o=new f(n.x+50*i.x,n.y+50*i.y,n.z),r=D.getInstance();r&&r.fireBullet(t,o,i,this._vehicleId,this.weaponType),w.instance.playSoundEffect("weaponFire"),this._currentAmmo<=0&&!this._isReloading&&this.startReload()}startReload(){this._isReloading=!0,this._reloadTimer=0,console.log("开始补充弹药...")}getCurrentAmmo(){return this._currentAmmo}getMaxAmmo(){return this.maxAmmo}isReloading(){return this._isReloading}getReloadProgress(){return this._isReloading?this._reloadTimer/this.ammoReloadTime:1}setAcceleration(t){console.log(`Player: 设置加速度为 ${t}`),this._accel=t}setDirection(t){console.log(`Player: 设置转向为 ${t}`),0!==t&&0===this._direction&&w.instance.playSoundEffect("carDrift"),this._direction=t}getAcceleration(){return this._accel}getDirection(){return this._direction}}).prototype,"maxSpeed",[et],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 50}}),k=e(M.prototype,"acceleration",[et],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 50}}),H=e(M.prototype,"brakeDeceleration",[et],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 200}}),K=e(M.prototype,"turnSpeed",[et],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 200}}),O=e(M.prototype,"friction",[et],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 1.5}}),W=e(M.prototype,"initAngle",[et],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 0}}),N=e(M.prototype,"maxHealth",[et],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 200}}),U=e(M.prototype,"destroyedSprite",[B],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),Y=e(M.prototype,"removeDelay",[et],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 3}}),G=e(M.prototype,"paintPrefab",[T],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),L=e(M.prototype,"paintSprayInterval",[et],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return.2}}),$=e(M.prototype,"normalBulletPrefab",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),J=e(M.prototype,"dartPrefab",[E],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),j=e(M.prototype,"rocketPrefab",[V],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),q=e(M.prototype,"fireRate",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 2}}),Q=e(M.prototype,"weaponType",[x],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return A.NORMAL}}),Z=e(M.prototype,"maxAmmo",[C],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 20}}),X=e(M.prototype,"ammoReloadTime",[z],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 10}}),I=M))||I));n._RF.pop()}}}));

System.register("chunks:///_virtual/PlayerInfoUI.ts",["./rollupPluginModLoBabelHelpers.js","cc","./PlayerManager.ts"],(function(e){var a,t,n,r,l,i,o;return{setters:[function(e){a=e.applyDecoratedDescriptor,t=e.initializerDefineProperty},function(e){n=e.cclegacy,r=e.Label,l=e._decorator,i=e.Component},function(e){o=e.PlayerManager}],execute:function(){var s,u,c,p,y,h,b;n._RF.push({},"66fbclPUjBL7oYA2xIdTwf+","PlayerInfoUI",void 0);const{ccclass:g,property:f}=l;e("PlayerInfoUI",(s=g("PlayerInfoUI"),u=f(r),c=f(r),s((h=a((y=class extends i{constructor(...e){super(...e),t(this,"levelLabel",h,this),t(this,"moneyLabel",b,this),this._playerManager=null}onLoad(){this._playerManager=o.instance,this._playerManager.addDataChangeListener(this.updateUI.bind(this)),this.updateUI(this._playerManager.playerData)}onDestroy(){this._playerManager&&this._playerManager.removeDataChangeListener(this.updateUI.bind(this))}updateUI(e){this.levelLabel&&(this.levelLabel.string=`level: ${e.level}`),this.moneyLabel&&(this.moneyLabel.string=`money: ${e.money}`)}}).prototype,"levelLabel",[u],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),b=a(y.prototype,"moneyLabel",[c],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),p=y))||p));n._RF.pop()}}}));

System.register("chunks:///_virtual/PlayerManager.ts",["cc"],(function(e){var a,t,s,r,l;return{setters:[function(e){a=e.cclegacy,t=e._decorator,s=e.Component,r=e.director,l=e.sys}],execute:function(){var n,i;a._RF.push({},"1b6d6aHKXNGGK9721LvZ7jB","PlayerManager",void 0);const{ccclass:o,property:c}=t;let h=e("LevelGrade",function(e){return e.S="S",e.A="A",e.B="B",e.C="C",e.D="D",e.F="F",e}({}));e("PlayerManager",o("PlayerManager")(((i=class e extends s{constructor(...e){super(...e),this.STORAGE_KEY="TopRacing_PlayerData",this.STORAGE_KEY_BACKUP="TopRacing_PlayerData_Backup",this._playerData=null,this._autoSaveInterval=3e4,this._lastAutoSaveTime=0,this._dataChangeCallbacks=[]}static get instance(){return e._instance}get playerData(){return this._playerData}onLoad(){e._instance?this.node.destroy():(e._instance=this,r.addPersistRootNode(this.node),this._initPlayerData(),this.loadPlayerData())}onDestroy(){e._instance===this&&(e._instance=null)}update(e){const a=Date.now();a-this._lastAutoSaveTime>this._autoSaveInterval&&(this._lastAutoSaveTime=a)}_initPlayerData(){this._playerData={level:1,money:0,experience:0,unlockedCars:[],currentCar:"",unlockedLevels:["level-1"],currentLevel:"level-1",levelProgress:{"level-1":{stars:0,completed:!1,bestTime:0,grade:h.F,attempts:0}},settings:{soundVolume:.8,musicVolume:.6},statistics:{totalRaces:0,totalWins:0,totalMoneyEarned:0},lastSaveTime:Date.now(),createTime:Date.now()}}async loadPlayerData(){try{let e=null;const a=l.localStorage.getItem(this.STORAGE_KEY);a&&(e=JSON.parse(a)),e?(this._playerData=this._mergePlayerData(this._playerData,e),console.log("玩家数据加载成功")):console.log("未找到存档数据，使用默认数据"),this._notifyDataChange()}catch(e){console.error("加载玩家数据失败:",e)}}async savePlayerData(){try{this._playerData.lastSaveTime=Date.now(),l.localStorage.setItem(this.STORAGE_KEY,JSON.stringify(this._playerData)),console.log("玩家数据保存成功")}catch(e){console.error("保存玩家数据失败:",e)}}_mergePlayerData(e,a){const t={...e};for(const e in a)a.hasOwnProperty(e)&&("object"!=typeof a[e]||null===a[e]||Array.isArray(a[e])?t[e]=a[e]:t[e]={...t[e],...a[e]});return t}addDataChangeListener(e){this._dataChangeCallbacks.push(e)}removeDataChangeListener(e){const a=this._dataChangeCallbacks.indexOf(e);-1!==a&&this._dataChangeCallbacks.splice(a,1)}_notifyDataChange(){this._dataChangeCallbacks.forEach((e=>{try{e(this._playerData)}catch(e){console.error("数据变化回调执行失败:",e)}}))}addMoney(e){this._playerData.money+=e,this._playerData.statistics.totalMoneyEarned+=e,this._notifyDataChange()}spendMoney(e){return this._playerData.money>=e&&(this._playerData.money-=e,this._notifyDataChange(),!0)}addExperience(e){this._playerData.experience+=e;const a=100*this._playerData.level;this._playerData.experience>=a&&(this._playerData.level++,this._playerData.experience-=a,console.log(`玩家升级到 ${this._playerData.level} 级！`)),this._notifyDataChange()}unlockCar(e){return-1===this._playerData.unlockedCars.indexOf(e)&&(this._playerData.unlockedCars.push(e),this._notifyDataChange(),!0)}isCarUnlocked(e){return-1!==this._playerData.unlockedCars.indexOf(e)}setCurrentCar(e){return-1!==this._playerData.unlockedCars.indexOf(e)&&(this._playerData.currentCar=e,this._notifyDataChange(),!0)}unlockLevel(e){return-1===this._playerData.unlockedLevels.indexOf(e)&&(this._playerData.unlockedLevels.push(e),this._playerData.levelProgress[e]={stars:0,completed:!1,bestTime:0,grade:h.F,attempts:0},this._notifyDataChange(),!0)}updateLevelProgress(e,a,t){let s;if(t)switch(t){case"S":s=h.S;break;case"A":s=h.A;break;case"B":s=h.B;break;case"F":case"failure":s=h.F;break;default:s=a>=3?h.S:a>=2?h.A:a>=1?h.B:h.F}else s=a>=3?h.S:a>=2?h.A:a>=1?h.B:h.F;const r=Date.now();if(this._playerData.levelProgress[e]){const t=this._playerData.levelProgress[e];a>t.stars&&(t.stars=a,t.bestTime=r,t.grade=s),t.completed=!0,t.attempts++}else this._playerData.levelProgress[e]={stars:a,completed:!0,bestTime:r,grade:s,attempts:1};this.checkAndUnlockNextLevel(e),this._notifyDataChange()}checkAndUnlockNextLevel(e){const a=this._playerData.levelProgress[e];if(a&&this.isGradePassable(a.grade)){const a=this.getNextLevelId(e);a&&-1===this._playerData.unlockedLevels.indexOf(a)&&(this.unlockLevel(a),console.log(`解锁新关卡: ${a}`))}}isGradePassable(e){return e!==h.F}getNextLevelId(e){const a=e.match(/level-(\d+)/);if(a){return`level-${parseInt(a[1])+1}`}return null}getLevelProgress(e){return this._playerData.levelProgress[e]||null}isLevelUnlocked(e){return-1!==this._playerData.unlockedLevels.indexOf(e)}getLevelGradeText(e){const a=this.getLevelProgress(e);return a&&a.completed?a.grade:""}getLevelGradeColor(e){switch(e){case h.S:return"#FFD700";case h.A:return"#C0C0C0";case h.B:return"#CD7F32";case h.F:return"#FF6B6B";default:return"#FFFFFF"}}updateSettings(e){this._playerData.settings={...this._playerData.settings,...e},this._notifyDataChange()}updateStatistics(e){this._playerData.statistics={...this._playerData.statistics,...e},this._notifyDataChange()}resetPlayerData(){this._initPlayerData(),this.savePlayerData(),this._notifyDataChange(),console.log("玩家数据已重置")}exportPlayerData(){return JSON.stringify(this._playerData,null,2)}importPlayerData(e){try{const a=JSON.parse(e);return this._playerData=this._mergePlayerData(this._playerData,a),this.savePlayerData(),this._notifyDataChange(),console.log("玩家数据导入成功"),!0}catch(e){return console.error("玩家数据导入失败:",e),!1}}})._instance=null,n=i))||n);a._RF.pop()}}}));

System.register("chunks:///_virtual/PurchasePanel.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(t){var i,e,n,o,r,l,s;return{setters:[function(t){i=t.applyDecoratedDescriptor,e=t.initializerDefineProperty},function(t){n=t.cclegacy,o=t.Label,r=t.Button,l=t._decorator,s=t.Component}],execute:function(){var c,a,u,h,p,b,f,C,m,d,y;n._RF.push({},"c4e94PebKNGoJIxF8tD6ORW","PurchasePanel",void 0);const{ccclass:B,property:P}=l;t("PurchasePanel",(c=B("PurchasePanel"),a=P({type:o,tooltip:"价格显示文本"}),u=P({type:o,tooltip:"车辆介绍文本"}),h=P({type:r,tooltip:"关闭按钮"}),p=P({type:r,tooltip:"购买确认按钮"}),c((C=i((f=class extends s{constructor(...t){super(...t),e(this,"priceLabel",C,this),e(this,"infoLabel",m,this),e(this,"closeButton",d,this),e(this,"confirmButton",y,this),this.currentPrice=0,this.onConfirmCallback=null}onLoad(){this.closeButton&&this.closeButton.node.on(r.EventType.CLICK,this.onCloseButtonClick,this),this.confirmButton&&this.confirmButton.node.on(r.EventType.CLICK,this.onConfirmButtonClick,this)}show(t,i,e){this.currentPrice=t,this.onConfirmCallback=e,this.priceLabel&&(this.priceLabel.string=`$${t}`),this.infoLabel&&(this.infoLabel.string=`${i}`),this.node&&(this.node.active=!0)}hide(){this.node&&(this.node.active=!1),this.onConfirmCallback=null}onCloseButtonClick(){this.hide()}onConfirmButtonClick(){this.onConfirmCallback&&this.onConfirmCallback(this.currentPrice),this.hide()}}).prototype,"priceLabel",[a],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),m=i(f.prototype,"infoLabel",[u],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),d=i(f.prototype,"closeButton",[h],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),y=i(f.prototype,"confirmButton",[p],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),b=f))||b));n._RF.pop()}}}));

System.register("chunks:///_virtual/SceneTransition.ts",["cc"],(function(i){var t,n,e,s,o,a,c;return{setters:[function(i){t=i.cclegacy,n=i._decorator,e=i.Component,s=i.director,o=i.UIOpacity,a=i.Widget,c=i.tween}],execute:function(){var r,l;t._RF.push({},"c1614ZteNpJT5/yalua4TMB","SceneTransition",void 0);const{ccclass:d}=n;i("SceneTransition",d("SceneTransition")(((l=class i extends e{constructor(...i){super(...i),this.uiOpacity=null,this.isTransitioning=!1}static getInstance(){return i.instance}static loadScene(t,n=.5,e=.5){const o=i.getInstance();o&&!o.isTransitioning?o.loadSceneWithTransition(t,n,e):(console.warn("SceneTransition not available, loading scene directly"),s.loadScene(t))}onLoad(){i.instance?this.node.destroy():(i.instance=this,s.addPersistRootNode(this.node),this.uiOpacity=this.getComponent(o),this.uiOpacity||(this.uiOpacity=this.node.addComponent(o)),this.setupFullScreenOverlay(),this.fadeIn(),console.log("SceneTransition initialized"))}setupFullScreenOverlay(){const i=this.node.getComponent(a)||this.node.addComponent(a);i.isAlignTop=!0,i.isAlignBottom=!0,i.isAlignLeft=!0,i.isAlignRight=!0,i.top=0,i.bottom=0,i.left=0,i.right=0,i.alignMode=a.AlignMode.ON_WINDOW_RESIZE,i.updateAlignment(),this.node.setSiblingIndex(999999)}fadeIn(i=.5){this.uiOpacity&&!this.isTransitioning&&(this.isTransitioning=!0,this.uiOpacity.opacity=255,c(this.uiOpacity).to(i,{opacity:0}).call((()=>{this.isTransitioning=!1})).start())}fadeOut(i,t=.5){this.uiOpacity&&!this.isTransitioning?(this.isTransitioning=!0,this.uiOpacity.opacity=0,c(this.uiOpacity).to(t,{opacity:255}).call((()=>{i&&i()})).start()):i&&i()}loadSceneWithTransition(i,t=.5,n=.5){this.isTransitioning?console.warn("Scene transition already in progress"):(console.log(`Starting scene transition to: ${i}`),this.fadeOut((()=>{s.loadScene(i,(()=>{console.log(`Scene transition to ${i} completed`)}))}),t))}onDestroy(){i.instance===this&&(i.instance=null)}}).instance=null,r=l))||r);t._RF.pop()}}}));

System.register("chunks:///_virtual/SelectManager.ts",["./rollupPluginModLoBabelHelpers.js","cc","./TempData.ts","./PlayerManager.ts","./SceneTransition.ts","./CarPropertyDisplay.ts","./PurchasePanel.ts"],(function(e){var t,n,i,o,r,a,s,c,l,u,p,h,g,d,y,f,C,m;return{setters:[function(e){t=e.applyDecoratedDescriptor,n=e.initializerDefineProperty},function(e){i=e.cclegacy,o=e.ToggleContainer,r=e.Button,a=e.Label,s=e.Node,c=e._decorator,l=e.Component,u=e.Sprite,p=e.Color,h=e.find,g=e.Toggle},function(e){d=e.TempData},function(e){y=e.PlayerManager},function(e){f=e.SceneTransition},function(e){C=e.CarPropertyDisplay},function(e){m=e.PurchasePanel}],execute:function(){var P,b,T,v,L,w,M,I,B,D,k,G,S,E,N,x,z;i._RF.push({},"be7b23A2jVN6agcMGkP3NKP","SelectManager",void 0);const{ccclass:K,property:A}=c;e("SelectManager",(P=K("SelectManager"),b=A(o),T=A(o),v=A(r),L=A(r),w=A(a),M=A({type:s,tooltip:"场景中的购买面板节点"}),I=A({type:C,tooltip:"car-property节点上的CarPropertyDisplay组件"}),P((k=t((D=class extends l{constructor(...e){super(...e),n(this,"levelToggleGroup",k,this),n(this,"carToggleGroup",G,this),n(this,"startButton",S,this),n(this,"backButton",E,this),n(this,"insufficientMoneyLabel",N,this),n(this,"purchasePanelNode",x,this),n(this,"carPropertyDisplay",z,this),this.carPrices={"car-1":0,"car-2":300,"car-3":500,"car-4":700,"car-5":800},this.carInfos={"car-1":"操控性超强小车，武器配备为子弹发射器，击中对手你可造成伤害 \n This super maneuverable car is equipped with a bullet launcher. When you hit your opponent, you can cause damage.","car-2":"经典跑车,具有坚固的车身,武器配备为火箭炮，爆炸后会清除附近的颜料 \nClassic sports car, with a sturdy body, equipped with  a rocket launcher. After explosion, it will clear the nearby paint.","car-3":"现代化的超级跑车，速度与转向均衡，配备武器为飞镖，攻守兼备 \nA modern supercar with balanced speed and steering, equipped with a dart launcher. it is a good choice for both attack and defense","car-4":"甩尾加速犹如闪电，武器配备为火箭炮，爆炸后会清除附近的颜料\nThe drift and acceleration is like lightning, equipped with a rocket launcher. After explosion, it will clear the nearby paint.","car-5":"送豆腐专用，即使在狭窄的山路也灵活穿梭，武器配备为飞镖，攻守兼备\n It is specially designed for delivering tofu and can move flexibly even on narrow mountain roads. equipped with a dart launcher. "},this.insufficientMoneyTimer=0,this.pendingCarId=null}onLoad(){this.updateLevelToggles(),this.updateCarToggles(),this.setupCarPurchaseButtons(),this.setupCarSelectionListener(),this.insufficientMoneyLabel&&(this.insufficientMoneyLabel.node.active=!1),this.autoFindCarPropertyDisplay()}updateLevelToggles(){const e=y.instance;console.log("更新关卡显示"),this.levelToggleGroup.toggleItems.forEach((t=>{const n=t.node.name,i=e.isLevelUnlocked(n);console.log(`关卡 ${n}: 解锁状态 = ${i}`),t.interactable=i;const o=t.node.getComponent(u),r=t.node.getChildByName("lock");o&&(o.color=i?p.WHITE:p.BLACK),r&&(r.active=!i),this.updateLevelGradeDisplay(t.node,n)}))}updateLevelGradeDisplay(e,t){const n=y.instance,i=n.getLevelGradeText(t);let o=e.getChildByName("GradeLabel");var r;o||(o=null==(r=e.getComponentInChildren(a))?void 0:r.node);if(o){const e=o.getComponent(a);if(e)if(i){e.string=i,e.node.active=!0;const o=n.getLevelProgress(t);if(o){const t=n.getLevelGradeColor(o.grade);e.color=this.hexToColor(t)}}else e.string="",e.node.active=!1}}hexToColor(e){const t=parseInt(e.slice(1,3),16),n=parseInt(e.slice(3,5),16),i=parseInt(e.slice(5,7),16);return new p(t,n,i,255)}updateCarToggles(){const e=y.instance.playerData.unlockedCars;this.carToggleGroup.toggleItems.forEach((t=>{const n=t.node.name,i=-1!==e.indexOf(n);t.interactable=i;const o=t.node.getComponent(u);o&&(o.color=i?p.WHITE:p.BLACK),this.updateCarPurchaseButton(t.node,n,i)}))}start(){this.startButton&&this.startButton.node.on(r.EventType.CLICK,this.onStartGame,this),this.backButton&&this.backButton.node.on(r.EventType.CLICK,this.onBackButton,this)}onStartGame(){const e=this.levelToggleGroup.toggleItems.find((e=>e.isChecked)),t=this.carToggleGroup.toggleItems.find((e=>e.isChecked));e&&t?(d.selectedLevel=e.node.name,d.selectedCar=t.node.name,console.log(e.node.name,t.node.name),f.loadScene("gamescene")):this.showMessage("请选择车辆！\n please select a car!")}onBackButton(){f.loadScene("mainmenu")}setupCarPurchaseButtons(){this.carToggleGroup.toggleItems.forEach((e=>{const t=e.node.name,n=-1!==y.instance.playerData.unlockedCars.indexOf(t);this.updateCarPurchaseButton(e.node,t,n)}))}updateCarPurchaseButton(e,t,n){let i=e.getChildByName("PurchaseButton");if(n||void 0===this.carPrices[t])i&&(i.active=!1);else if(i||(i=e.getChildByName("PurchaseButton")),i){i.active=!0;const e=i.getComponent(r);e&&(e.node.off(r.EventType.CLICK),e.node.on(r.EventType.CLICK,(()=>{this.pendingCarId=t,this.showPurchasePanel(this.carPrices[t],this.carInfos[t])}),this))}}showPurchasePanel(e,t){if(!this.purchasePanelNode)return void console.error("购买面板节点未配置");const n=this.purchasePanelNode.getComponent(m);n?n.show(e,t,(e=>{this.processPurchase(e)})):console.error("购买面板组件未找到")}processPurchase(e){if(!this.pendingCarId)return;const t=this.pendingCarId,n=y.instance;n.playerData.money>=e?n.spendMoney(e)&&(n.unlockCar(t),console.log(`成功购买车辆 ${t}，花费 ${e} 金币`),this.updateCarToggles(),n.savePlayerData()):this.showInsufficientMoneyMessage(),this.pendingCarId=null}showInsufficientMoneyMessage(){this.insufficientMoneyLabel&&(this.insufficientMoneyLabel.string="金币不足！\n your money is not enough",this.insufficientMoneyLabel.node.active=!0,this.insufficientMoneyTimer=3)}showMessage(e){this.insufficientMoneyLabel&&(this.insufficientMoneyLabel.string=e,this.insufficientMoneyLabel.node.active=!0,this.insufficientMoneyTimer=3)}update(e){this.insufficientMoneyTimer>0&&(this.insufficientMoneyTimer-=e,this.insufficientMoneyTimer<=0&&this.insufficientMoneyLabel&&(this.insufficientMoneyLabel.node.active=!1))}getCarPrice(e){return this.carPrices[e]||0}setCarPrice(e,t){this.carPrices[e]=t}autoFindCarPropertyDisplay(){if(!this.carPropertyDisplay){const e=h("Canvas/car-property")||h("car-property")||this.node.getChildByName("car-property");e?(this.carPropertyDisplay=e.getComponent(C),this.carPropertyDisplay||console.warn("car-property节点找到了，但没有CarPropertyDisplay组件")):console.warn("未找到car-property节点，请确保场景中存在该节点")}}setupCarSelectionListener(){this.carToggleGroup?(this.carToggleGroup.toggleItems.forEach((e=>{e.node.on(g.EventType.TOGGLE,this.onCarToggleChanged,this)})),this.checkInitialCarSelection()):console.warn("carToggleGroup未设置")}onCarToggleChanged(e){if(e.isChecked){const t=e.node.name;console.log(`选中车辆: ${t}`),this.showCarProperties(t)}}showCarProperties(e){this.carPropertyDisplay?this.carPropertyDisplay.showCarProperties(e):console.warn("CarPropertyDisplay组件未找到，无法显示车辆属性")}checkInitialCarSelection(){const e=this.carToggleGroup.toggleItems.find((e=>e.isChecked));if(e){const t=e.node.name;this.showCarProperties(t)}}}).prototype,"levelToggleGroup",[b],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),G=t(D.prototype,"carToggleGroup",[T],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),S=t(D.prototype,"startButton",[v],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),E=t(D.prototype,"backButton",[L],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),N=t(D.prototype,"insufficientMoneyLabel",[w],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),x=t(D.prototype,"purchasePanelNode",[M],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),z=t(D.prototype,"carPropertyDisplay",[I],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),B=D))||B));i._RF.pop()}}}));

System.register("chunks:///_virtual/SoundManager.ts",["./rollupPluginModLoBabelHelpers.js","cc"],(function(t){var e,i,o,r,u,c,a,s;return{setters:[function(t){e=t.applyDecoratedDescriptor,i=t.initializerDefineProperty},function(t){o=t.cclegacy,r=t.AudioSource,u=t._decorator,c=t.Component,a=t.director,s=t.sys}],execute:function(){var l,n,d,S,h,p,A,b,f,g,m,y,D,k,v,C,M,w;o._RF.push({},"7c3850Yg29IBK4wW9FYz7f1","SoundManager",void 0);const{ccclass:z,property:_}=u;t("SoundManager",(l=z("SoundManager"),n=_({type:r,tooltip:"背景音乐"}),d=_({type:r,tooltip:"按钮点击音效"}),S=_({type:r,tooltip:"车辆碰撞音效"}),h=_({type:r,tooltip:"车辆毁坏音效"}),p=_({type:r,tooltip:"车辆启动音效"}),A=_({type:r,tooltip:"车辆加速音效"}),b=_({type:r,tooltip:"车辆漂移音效"}),l(((w=class t extends c{constructor(...t){super(...t),i(this,"bgmAudioSource",m,this),i(this,"buttonClickAudioSource",y,this),i(this,"carCollisionAudioSource",D,this),i(this,"carDestructionAudioSource",k,this),i(this,"carStartAudioSource",v,this),i(this,"carAccelerateAudioSource",C,this),i(this,"carDriftAudioSource",M,this),this.allAudioSources=[]}static get instance(){return this._instance}onLoad(){t._instance?this.destroy():(t._instance=this,a.addPersistRootNode(this.node),this.allAudioSources.push(this.bgmAudioSource),this.allAudioSources.push(this.buttonClickAudioSource),this.allAudioSources.push(this.carCollisionAudioSource),this.allAudioSources.push(this.carDestructionAudioSource),this.allAudioSources.push(this.carStartAudioSource),this.allAudioSources.push(this.carAccelerateAudioSource),this.allAudioSources.push(this.carDriftAudioSource),this.loadState())}start(){this.playBGM()}playBGM(){this.bgmAudioSource&&this.bgmAudioSource.play()}playSoundEffect(t){let e=null;switch(t){case"buttonClick":e=this.buttonClickAudioSource;break;case"carCollision":e=this.carCollisionAudioSource;break;case"carDestruction":e=this.carDestructionAudioSource;break;case"carStart":e=this.carStartAudioSource;break;case"carAccelerate":e=this.carAccelerateAudioSource;break;case"carDrift":e=this.carDriftAudioSource}e&&e.play()}toggleAudio(){const t=this.isMuted()?1:0;this.allAudioSources.forEach((e=>{e===this.bgmAudioSource?e.volume=.3*t:e&&(e.volume=t)})),this.saveState()}isMuted(){return!!this.bgmAudioSource&&0===this.bgmAudioSource.volume}saveState(){const t={muted:this.isMuted()};s.localStorage.setItem("soundState",JSON.stringify(t))}loadState(){const t=s.localStorage.getItem("soundState");if(t){const e=JSON.parse(t).muted?0:1;this.allAudioSources.forEach((t=>{t===this.bgmAudioSource?t.volume=.3*e:t&&(t.volume=e)}))}}})._instance=null,m=e((g=w).prototype,"bgmAudioSource",[n],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),y=e(g.prototype,"buttonClickAudioSource",[d],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),D=e(g.prototype,"carCollisionAudioSource",[S],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),k=e(g.prototype,"carDestructionAudioSource",[h],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),v=e(g.prototype,"carStartAudioSource",[p],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),C=e(g.prototype,"carAccelerateAudioSource",[A],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),M=e(g.prototype,"carDriftAudioSource",[b],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return null}}),f=g))||f));o._RF.pop()}}}));

System.register("chunks:///_virtual/TempData.ts",["cc"],(function(e){var t;return{setters:[function(e){t=e.cclegacy}],execute:function(){t._RF.push({},"4702aLSiftLiKCAlxKVKQG+","TempData",void 0);class c{}e("TempData",c),c.selectedLevel="",c.selectedCar="",t._RF.pop()}}}));

(function(r) {
  r('virtual:///prerequisite-imports/main', 'chunks:///_virtual/main'); 
})(function(mid, cid) {
    System.register(mid, [cid], function (_export, _context) {
    return {
        setters: [function(_m) {
            var _exportObj = {};

            for (var _key in _m) {
              if (_key !== "default" && _key !== "__esModule") _exportObj[_key] = _m[_key];
            }
      
            _export(_exportObj);
        }],
        execute: function () { }
    };
    });
});