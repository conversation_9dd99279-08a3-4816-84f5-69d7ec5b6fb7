[1, ["4c8ZSK/8pCbpxDHnyGShtg@f9941", "59OQTy+oVJj5sOn3mJDCT9@f9941", "a4JsLIWLFOWqFs51BhotA0@f9941", "6aAXnbsLxJBYlTirB2o8/W@f9941", "1cGPbnOxNAG4WhdGv3KElJ@f9941", "1cGPbnOxNAG4WhdGv3KElJ@6c48a", "1cUFL6iBlGvLuVGqkLhz4u@6c48a", "4bKpDnllRD97GcGSm/3B+p@6c48a", "83uu73dGtPkZqE/YpY0riz@6c48a", "1cUFL6iBlGvLuVGqkLhz4u@f9941", "dd2z2XW55MyKzf+eRFOslI@f9941", "4bKpDnllRD97GcGSm/3B+p@f9941", "83uu73dGtPkZqE/YpY0riz@f9941", "81Ugz1OgJGO7PsyIcKP0lA", "b2s5pmJWRL6IgC0EPRL/2L@f9941", "c4kqHHgpBNcZj/pLuCLLBn", "050J0q0eZNtLfxRsgoVh4c", "dfFrs6vHdHZrrnJ2kxbtbl", "9fho2hieRMpqThK1kpgdsh", "c18s46WqhFuqs+X/+LuHr5", "dd2z2XW55MyKzf+eRFOslI@6c48a"], ["node", "_spriteFrame", "_textureSource", "_clip", "_normalSprite", "targetInfo", "_parent", "_cameraComponent", "carDriftAudioSource", "carStartAudioSource", "carDestructionAudioSource", "buttonClickAudioSource", "bgmAudioSource", "root", "scene", "asset", "value"], [["cc.Node", ["_name", "_layer", "_id", "_obj<PERSON><PERSON>s", "_active", "__editorExtras__", "_components", "_parent", "_lpos", "_children", "_prefab"], -3, 9, 1, 5, 2, 4], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_isBold", "_enableOutline", "_enableShadow", "_lineHeight", "_enableWrapText", "_overflow", "node", "_color", "_shadowColor", "_outlineColor"], -6, 1, 5, 5, 5], "cc.SpriteFrame", ["cc.Node", ["_name", "_layer", "_id", "_components", "_parent", "_lpos", "_children"], 0, 12, 1, 5, 2], ["cc.Sprite", ["_sizeMode", "_type", "node", "_spriteFrame", "_color"], 1, 1, 6, 5], ["cc.Widget", ["_alignFlags", "_top", "_bottom", "_alignMode", "_right", "_isAbsRight", "_isAbsTop", "_originalHeight", "node", "_target"], -5, 1, 1], ["cc.AudioSource", ["_playOnAwake", "_loop", "_volume", "node"], 0, 1], ["cc.<PERSON><PERSON>", ["_transition", "node", "_normalColor", "_target"], 2, 1, 5, 1], ["cc.SceneAsset", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_lpos"], 2, 1, 2, 5], ["cc.UITransform", ["node", "_contentSize"], 3, 1, 5], ["cc.<PERSON>", ["_alignCanvasWithScreen", "node", "_cameraComponent"], 2, 1, 1], ["7c3850Yg29IBK4wW9FYz7f1", ["node", "bgmAudioSource", "buttonClickAudioSource", "carDestructionAudioSource", "carStartAudioSource", "carDriftAudioSource"], 3, 1, 1, 1, 1, 1, 1], ["cc.Scene", ["_name", "autoReleaseAssets", "_children", "_prefab", "_globals"], 1, 2, 4, 4], ["cc.PrefabInfo", ["root", "asset", "fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots"], -2, 2], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.SceneGlobals", ["ambient", "shadows", "_skybox", "fog", "octree", "skin", "lightProbeInfo", "postSettings"], 3, 4, 4, 4, 4, 4, 4, 4, 4], ["cc.AmbientInfo", ["_skyColorHDR", "_groundAlbedoHDR"], 3, 5, 5], ["cc.ShadowsInfo", ["_shadowColor", "_size"], 3, 5, 5], ["cc.SkyboxInfo", [], 3], ["cc.FogInfo", [], 3], ["cc.OctreeInfo", [], 3], ["cc.SkinInfo", ["_enabled"], 2], ["cc.LightProbeInfo", [], 3], ["cc.PostSettingsInfo", [], 3], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides"], 1, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 6], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["cc.TargetInfo", ["localID"], 2], ["cc.Camera", ["_projection", "_orthoHeight", "_near", "_visibility", "node", "_color"], -1, 1, 5], ["1b6d6aHKXNGGK9721LvZ7jB", ["node"], 3, 1], ["0cf64BckYpA8bODaKn8c5t/", ["node", "startGameBtn", "settingBtn", "closesettingBtn", "audioBtn", "settingPanel", "audioLabel", "helpButton", "closehelpBtn", "helpPanel", "resetProgressBtn", "resetProgressConfirmPanel", "confirmResetBtn", "closeResetPanelBtn"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]], [[10, 0, 1, 1], [4, 0, 2, 3, 2], [0, 0, 1, 7, 6, 8, 3], [6, 0, 3, 2], [30, 0, 2], [3, 0, 1, 4, 6, 3, 5, 3], [7, 0, 1, 2], [3, 0, 1, 4, 3, 5, 3], [27, 0, 1, 2, 2], [7, 0, 1, 2, 3, 2], [0, 0, 4, 1, 7, 9, 6, 8, 4], [0, 0, 3, 1, 7, 6, 4], [4, 1, 0, 2, 4, 3, 3], [0, 0, 1, 7, 6, 3], [0, 0, 2, 7, 6, 3], [4, 2, 3, 1], [1, 0, 1, 2, 3, 4, 5, 9, 10, 7], [1, 0, 1, 2, 6, 9, 10, 5], [8, 0, 2], [0, 0, 1, 2, 9, 6, 8, 4], [0, 3, 5, 7, 10, 3], [3, 0, 2, 3, 3], [3, 0, 1, 4, 3, 3], [9, 0, 1, 2, 3, 2], [11, 0, 1, 2, 2], [5, 0, 1, 2, 3, 8, 5], [5, 0, 4, 1, 2, 5, 6, 7, 8, 9, 8], [12, 0, 1, 2, 3, 4, 5, 1], [6, 1, 2, 3, 3], [4, 1, 0, 2, 3, 3], [13, 0, 1, 2, 3, 4, 3], [14, 0, 1, 2, 3, 4, 5, 6], [15, 0, 1, 2, 3, 4, 5, 4], [16, 0, 1, 2, 3, 4, 5, 6, 7, 1], [17, 0, 1, 1], [18, 0, 1, 1], [19, 1], [20, 1], [21, 1], [22, 0, 2], [23, 1], [24, 1], [1, 0, 1, 2, 6, 7, 3, 4, 5, 9, 10, 9], [1, 0, 1, 2, 6, 7, 3, 4, 5, 9, 10, 12, 11, 9], [1, 0, 1, 2, 6, 7, 3, 4, 5, 9, 10, 11, 9], [1, 0, 1, 2, 8, 3, 4, 5, 9, 10, 8], [1, 0, 1, 2, 6, 8, 3, 4, 5, 9, 10, 9], [25, 0, 1, 2, 3], [26, 0, 1, 2, 3], [28, 0, 1, 2, 2], [29, 0, 1, 2, 2], [31, 0, 1, 2, 3, 4, 5, 5], [32, 0, 1], [33, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 1]], [[[{"name": "green", "rect": {"x": 0, "y": 0, "width": 270, "height": 206}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 270, "height": 206}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-135, -103, 0, 135, -103, 0, -135, 103, 0, 135, 103, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 206, 270, 206, 0, 0, 270, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -135, "y": -103, "z": 0}, "maxPos": {"x": 135, "y": 103, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [2], 0, [0], [2], [5]], [[{"name": "background1", "rect": {"x": 0, "y": 0, "width": 1536, "height": 864}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 1536, "height": 864}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-768, -432, 0, 768, -432, 0, -768, 432, 0, 768, 432, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 864, 1536, 864, 0, 0, 1536, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -768, "y": -432, "z": 0}, "maxPos": {"x": 768, "y": 432, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [2], 0, [0], [2], [6]], [[{"name": "setting", "rect": {"x": 0, "y": 0, "width": 1067, "height": 1054}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 1067, "height": 1054}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-533.5, -527, 0, 533.5, -527, 0, -533.5, 527, 0, 533.5, 527, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 1054, 1067, 1054, 0, 0, 1067, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -533.5, "y": -527, "z": 0}, "maxPos": {"x": 533.5, "y": 527, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [2], 0, [0], [2], [7]], [[{"name": "traffic", "rect": {"x": 0, "y": 0, "width": 640, "height": 1536}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 640, "height": 1536}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-320, -768, 0, 320, -768, 0, -320, 768, 0, 320, 768, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 1536, 640, 1536, 0, 0, 640, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -320, "y": -768, "z": 0}, "maxPos": {"x": 320, "y": 768, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [2], 0, [0], [2], [8]], [[[18, "mainmenu"], [19, "<PERSON><PERSON>", 33554432, "beI88Z2HpFELqR4T5EMHpg", [-5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16], [[0, -1, [5, 1280, 720]], [24, false, -3, -2], [25, 45, 5.684341886080802e-14, 5.684341886080802e-14, 1, -4]], [1, 640, 360.00000000000006, 0]], [21, "SoundManager", "f8zc0C52RFyJSZjqngqV7J", [[[27, -22, -21, -20, -19, -18, -17], -23, -24, -25, -26, -27, [3, false, -28], [3, false, -29]], 4, 1, 1, 1, 1, 1, 4, 4]], [10, "SettingPanel", false, 33554432, 1, [-32, -33, -34], [[0, -30, [5, 509, 558]], [15, -31, 9]], [1, 0.8537647541830893, -30.046650789554917, 0]], [10, "HelpPanel", false, 33554432, 1, [-37, -38, -39], [[0, -35, [5, 850.965, 702.41]], [1, 0, -36, 11]], [1, 21.627, 0.529, 0]], [10, "ResetPanel", false, 33554432, 1, [-42, -43, -44], [[0, -40, [5, 509, 558]], [15, -41, 16]], [1, 0.8537647541830893, -30.046650789554917, 0]], [5, "startButton", 33554432, 1, [-48], [[[0, -45, [5, 248.865, 71.20889379286326]], [12, 1, 0, -46, [4, 4293519859], 2], -47], 4, 4, 1], [1, 0.9, -80.462, 0]], [5, "resetButton", 33554432, 1, [-52], [[[0, -49, [5, 257.85, 73.77981341084441]], [12, 1, 0, -50, [4, 4293519859], 3], -51], 4, 4, 1], [1, 2.472, -164.535, 0]], [5, "helpButton", 33554432, 1, [-56], [[[0, -53, [5, 257.85, 73.77981341084441]], [12, 1, 0, -54, [4, 4293519859], 4], -55], 4, 4, 1], [1, 2.472, -254.486, 0]], [7, "settingButton", 33554432, 1, [[[0, -57, [5, 66.17800000000001, 67.19247971371924]], [29, 1, 0, -58, 5], -59, [26, 33, 0.026357031250000166, 0.04493994464325066, 335, false, false, 50, -60, 1]], 4, 4, 1, 4], [1, 573.1739999999998, 294.0469999999999, 0]], [30, "mainmenu", true, [1, -62, -63, 2], [31, null, null, "9a4dce19-24b9-4d46-b38f-ef49f2287dcb", null, null, [-61]], [33, [34, [2, 0, 0, 0, 0.520833125], [2, 0, 0, 0, 0]], [35, [4, 4283190348], [0, 512, 512]], [36], [37], [38], [39, false], [40], [41]]], [5, "audioButton", 33554432, 3, [-67], [[[0, -64, [5, 280.995, 81.87200000000001]], [1, 0, -65, 8], -66], 4, 4, 1], [1, 0, -179.864, 0]], [5, "confirmButton", 33554432, 5, [-71], [[[0, -68, [5, 280.995, 81.87200000000001]], [1, 0, -69, 15], -70], 4, 4, 1], [1, 0, -204.353, 0]], [7, "close", 33554432, 3, [[[0, -72, [5, 55.50500000000002, 55.50500000000002]], [1, 0, -73, 7], -74], 4, 4, 1], [1, 212.483, 235.404, 0]], [7, "close", 33554432, 4, [[[0, -75, [5, 55.50500000000002, 55.50500000000002]], [1, 0, -76, 10], -77], 4, 4, 1], [1, 367.486, 300.922, 0]], [7, "close", 33554432, 5, [[[0, -78, [5, 55.50500000000002, 55.50500000000002]], [1, 0, -79, 14], -80], 4, 4, 1], [1, 212.483, 235.404, 0]], [13, "background", 33554432, 1, [[0, -81, [5, 1650.8790000000001, 927.8346875]], [1, 0, -82, 0]]], [2, "logo", 33554432, 1, [[0, -83, [5, 488.76500000000004, 357.68999999999994]], [1, 0, -84, 1]], [1, -22.533000000000015, 181.15499999999992, 0]], [11, "Label", 512, 33554432, 6, [[0, -85, [5, 219.0244140625, 41.8]], [42, "单人游戏 single", 30, 30, 30, false, true, true, true, -86, [4, 4282203594]]]], [11, "Label", 512, 33554432, 7, [[0, -87, [5, 204.0537109375, 41.8]], [43, "重制进度 reset", 30, 30, 30, false, true, true, true, -88, [4, 4284079342], [4, 4278192908], [4, 4278394139]]]], [11, "Label", 512, 33554432, 8, [[0, -89, [5, 194.0048828125, 41.8]], [44, "游戏说明 help", 30, 30, 30, false, true, true, true, -90, [4, 4288536406], [4, 4278588938]]]], [2, "traffic", 33554432, 1, [[0, -91, [5, 124.47400000000002, 298.7376]], [1, 0, -92, 6]], [1, -190.725, -174.653, 0]], [2, "soundtext", 33554432, 3, [[0, -93, [5, 390.509033203125, 94.39999999999999]], [45, "打开或关闭声音\nturn on / turn off the sound effect", 24, 24.5, 2, true, true, true, -94, [4, 4287005403]]], [1, 0, 127.337, 0]], [22, "Label", 33554432, 11, [[[0, -95, [5, 91.19140625, 56.047999999999995]], -96], 4, 1]], [2, "title", 33554432, 4, [[0, -97, [5, 188.173583984375, 54.4]], [16, "help/帮助", 42.5, 42.5, true, true, true, -98, [4, 4286765690]]], [1, -7.99, 302.016, 0]], [2, "content", 33554432, 4, [[0, -99, [5, 779.6208437500001, 508.804]], [46, "不同的车辆拥有不同特性和武器，用出色的驾驶和甩尾技术在有限的时间内喷洒更多颜料！\n必要时，可使用车辆的专属武器攻击对手/移除对手已喷洒的燃料\n倒计时结束后，依据玩家颜料占比进行评分\nS:45%  A:35%  B:25%  F:<25%\n\nDifferent cars have different features and weapons. \nUse your excellent driving and drifting skills \nto spray more paint within the limited time! \nWhen necessary, use the exclusive weapon to attack your opponents or remove the fuel they have sprayed.\nAfter the countdown ends, players will be scored based on their proportion of paint. \nS:45%  A:35%  B:25%  F:<25%", 25.6, 25.6, 35.4, 3, true, true, true, -100, [4, 4292519892]]], [1, 5.428, -21.068, 0]], [20, 0, {}, 1, [32, "98m1scCCpHFaAD50RjkFIZ", null, null, -103, [47, "3bBm5N8+NLeYdVfjOnRTcE", null, [[48, "loading", ["_name"], [4, ["98m1scCCpHFaAD50RjkFIZ"]]], [8, ["_lpos"], [4, ["98m1scCCpHFaAD50RjkFIZ"]], [1, -0.0004999999999597549, 0, 0]], [8, ["_lrot"], [4, ["98m1scCCpHFaAD50RjkFIZ"]], [3, 0, 0, 0, 1]], [8, ["_euler"], [4, ["98m1scCCpHFaAD50RjkFIZ"]], [1, 0, 0, 0]], [8, ["_contentSize"], [4, ["dftExsNEZHn4dtPomyMxdT"]], [5, 1650.8790000000001, 927.834]], [49, ["_spriteFrame"], -101, 13], [50, ["_color"], -102, [4, 4289835451]]]], 12]], [13, "Label", 33554432, 12, [[0, -104, [5, 65.56640625, 56.047999999999995]], [17, "确认\nconfirm", 20, 20, 24.8, -105, [4, 4280967838]]]], [2, "text", 33554432, 5, [[0, -106, [5, 409.6884765625, 94.39999999999999]], [16, "确认要重制进度吗？\ncomfirm to reset player progress?", 25, 25, true, true, true, -107, [4, 4283001582]]], [1, 0, 139.085, 0]], [23, "Camera", 1, [-108], [1, 0, 0, 1000]], [51, 0, 360, 0, 1108344832, 29, [4, 4278190080]], [9, 3, 6, [4, 4292269782], 6], [9, 3, 7, [4, 4292269782], 7], [9, 3, 8, [4, 4292269782], 8], [9, 3, 9, [4, 4292269782], 9], [6, 3, 13], [17, "音效:开\nSound: on", 20, 20, 24.8, 23, [4, 4280967838]], [6, 3, 11], [6, 3, 14], [4, ["50p6T0zeZHzqrT8IjFBzum"]], [6, 3, 15], [6, 3, 12], [14, "<PERSON><PERSON><PERSON><PERSON>", "ef0ZsUXShJ9pBKMQh7xWv7", 10, [[52, -109]]], [14, "MainMenuController", "1eIGv73uxJ149Ou4e8BJTt", 10, [[53, -110, 31, 34, 35, 37, 3, 36, 33, 38, 4, 32, 5, 41, 40]]], [28, true, 0.5, 2], [3, false, 2], [3, false, 2], [3, false, 2], [3, false, 2]], 0, [0, 0, 1, 0, 7, 30, 0, 0, 1, 0, 0, 1, 0, -1, 29, 0, -2, 16, 0, -3, 17, 0, -4, 6, 0, -5, 7, 0, -6, 8, 0, -7, 9, 0, -8, 21, 0, -9, 3, 0, -10, 4, 0, -11, 26, 0, -12, 5, 0, 8, 48, 0, 9, 47, 0, 10, 46, 0, 11, 45, 0, 12, 44, 0, 0, 2, 0, -2, 44, 0, -3, 45, 0, -4, 47, 0, -5, 48, 0, -6, 46, 0, 0, 2, 0, 0, 2, 0, 0, 3, 0, 0, 3, 0, -1, 22, 0, -2, 13, 0, -3, 11, 0, 0, 4, 0, 0, 4, 0, -1, 14, 0, -2, 24, 0, -3, 25, 0, 0, 5, 0, 0, 5, 0, -1, 15, 0, -2, 12, 0, -3, 28, 0, 0, 6, 0, 0, 6, 0, -3, 31, 0, -1, 18, 0, 0, 7, 0, 0, 7, 0, -3, 32, 0, -1, 19, 0, 0, 8, 0, 0, 8, 0, -3, 33, 0, -1, 20, 0, 0, 9, 0, 0, 9, 0, -3, 34, 0, 0, 9, 0, -1, 26, 0, -2, 42, 0, -3, 43, 0, 0, 11, 0, 0, 11, 0, -3, 37, 0, -1, 23, 0, 0, 12, 0, 0, 12, 0, -3, 41, 0, -1, 27, 0, 0, 13, 0, 0, 13, 0, -3, 35, 0, 0, 14, 0, 0, 14, 0, -3, 38, 0, 0, 15, 0, 0, 15, 0, -3, 40, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, -2, 36, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 5, 39, 0, 5, 39, 0, 13, 26, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, -1, 30, 0, 0, 42, 0, 0, 43, 0, 14, 10, 1, 6, 10, 2, 6, 10, 110], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 31, 32, 33, 44, 45, 46, 47, 48], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 15, 16, 1, 1, 1, 4, 4, 4, 3, 3, 3, 3, 3], [9, 10, 0, 1, 4, 11, 12, 2, 0, 3, 2, 3, 13, 14, 2, 1, 3, 0, 1, 4, 15, 16, 17, 18, 19]], [[{"name": "logo", "rect": {"x": 0, "y": 0, "width": 1000, "height": 1000}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 1000, "height": 1000}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-500, -500, 0, 500, -500, 0, -500, 500, 0, 500, 500, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 1000, 1000, 1000, 0, 0, 1000, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -500, "y": -500, "z": 0}, "maxPos": {"x": 500, "y": 500, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [2], 0, [0], [2], [20]]]]